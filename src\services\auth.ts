import api from '../utils/api'

// 用户相关的API接口类型定义
export interface User {
  id: number
  username: string
  email: string
  real_name: string
  student_id: string
  school: string
  college: string
  major: string
  enrollment_year: number | null
  bio: string
  avatar: string | null
  email_verified: boolean
  created_at: string
}

export interface LoginData {
  email: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  password2: string
  real_name?: string
  student_id?: string
  school?: string
  college?: string
  major?: string
  enrollment_year?: number
}

export interface LoginResponse {
  token: string
  user: User
}

export interface ChangePasswordData {
  old_password: string
  new_password: string
}

// 用户认证API
export const authAPI = {
  // 用户登录
  login: (data: LoginData): Promise<LoginResponse> => {
    return api.post('/users/login/', data)
  },

  // 用户注册
  register: (data: RegisterData): Promise<User> => {
    return api.post('/users/register/', data)
  },

  // 获取用户信息
  getProfile: (): Promise<User> => {
    return api.get('/users/profile/')
  },

  // 更新用户信息
  updateProfile: (data: Partial<User>): Promise<User> => {
    return api.patch('/users/profile/', data)
  },

  // 修改密码
  changePassword: (data: ChangePasswordData): Promise<void> => {
    return api.put('/users/change-password/', data)
  }
}

export default authAPI
