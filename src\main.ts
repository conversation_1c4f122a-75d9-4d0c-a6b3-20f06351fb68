import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import routes from './router/index'
import { useUserStore } from './stores/user'

const app = createApp(App)

const router = createRouter({
  history: createWebHistory(),
  routes
})

const pinia = createPinia()

app.use(router)
app.use(pinia)

// Pinia store 必须在 app.use(pinia) 之后才能被正确初始化
const userStore = useUserStore()

// 初始化用户状态，这一步会从localStorage加载token
userStore.initUserFromStorage()

// 添加路由守卫
router.beforeEach((to, from, next) => {
  const isLoggedIn = !!userStore.token;
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const isAuthPage = to.name === 'Login' || to.name === 'Register';

  if (requiresAuth && !isLoggedIn) {
    // 1. 目标页面需要登录，但用户未登录
    // 重定向到登录页，并带上原始目标路径，以便登录后跳转回来
    next({ name: 'Login', query: { redirect: to.fullPath } });
  } else if (isLoggedIn && isAuthPage) {
    // 2. 用户已登录，但访问的是登录/注册页
    // 重定向到仪表盘
    next({ name: 'Dashboard' });
  } else {
    // 3. 其他所有情况（已登录访问非认证页，或未登录访问公共页）
    // 正常放行
    next();
  }
})

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(ElementPlus)

app.mount('#app')
