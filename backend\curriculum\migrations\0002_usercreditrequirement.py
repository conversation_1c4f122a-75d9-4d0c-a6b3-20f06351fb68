# Generated by Django 5.2.3 on 2025-06-24 09:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('curriculum', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserCreditRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(max_length=100, verbose_name='课程类别')),
                ('required_credits', models.PositiveIntegerField(verbose_name='要求学分')),
                ('description', models.TextField(blank=True, null=True, verbose_name='要求描述')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_customizations', to='curriculum.curriculumtemplate', verbose_name='培养方案模板')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_credit_requirements', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户自定义学分要求',
                'verbose_name_plural': '用户自定义学分要求',
                'db_table': 'user_credit_requirements',
                'unique_together': {('user', 'template', 'category')},
            },
        ),
    ]
