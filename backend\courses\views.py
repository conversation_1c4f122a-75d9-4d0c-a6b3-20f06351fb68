from django.shortcuts import render
from rest_framework import generics, permissions
from .models import Course, UserCourse
from .serializers import CourseSerializer, UserCourseSerializer

# Create your views here.

class CourseListView(generics.ListAPIView):
    """课程列表API"""
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = [permissions.IsAuthenticated]

class CourseDetailView(generics.RetrieveAPIView):
    """课程详情API"""
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = [permissions.IsAuthenticated]

class UserCourseListCreateView(generics.ListCreateAPIView):
    """用户课程列表和创建API"""
    serializer_class = UserCourseSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserCourse.objects.filter(user=self.request.user)

class UserCourseDetailView(generics.RetrieveUpdateDestroyAPIView):
    """用户课程详情、更新、删除API"""
    serializer_class = UserCourseSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserCourse.objects.filter(user=self.request.user)
