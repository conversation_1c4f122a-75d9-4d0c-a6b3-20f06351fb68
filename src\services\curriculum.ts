import api from '../utils/api'

// ===================================
// 新的、基于规则的类型定义
// ===================================

export interface CourseTag {
  id: number;
  name: string;
  description: string;
}

export interface Rule {
  id: number;
  name: string;
  description: string;
  credits_required: number;
  courses_required: number;
  credits_completed: number;
  courses_completed: number;
  is_met: boolean;
  tags: CourseTag[];
  tag_match_logic: 'ANY' | 'ALL';
}

export interface MetCourse {
    courseCode: string;
    courseName: string;
    credits: number;
}

export interface RequirementModule {
  id: number;
  name: string;
  description: string;
  credits_required: number;
  credits_completed: number;
  courses_required: number;
  courses_completed: number;
  is_met: boolean;
  rules: Rule[];
  children: RequirementModule[];
  met_courses: MetCourse[];
}

export interface AuditResultSummary {
  overallStatus: 'passed' | 'failed';
  summary: string;
  totalRequirements: number;
  metRequirements: number;
  unmetRequirements: number;
  auditDate: string;
}

export interface FullAuditReport {
  auditResult: AuditResultSummary;
  auditDetails: RequirementModule[];
  template: {
    id: number;
    school: string;
    major: string;
    totalCreditsRequired: number;
  }
}

export interface Course {
  id: number;
  course_code: string;
  course_name: string;
  credits: number;
  tags: CourseTag[];
  description: string;
  prerequisites: number[];
}

// ===================================
// 旧的类型定义 (部分保留和更新)
// ===================================

export interface CurriculumTemplate {
  id: number
  school: string
  major: string
  enrollment_year: string
  total_credits_required: number
  description: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface TemplateCourse {
  id: number
  course: Course
  recommended_semester: number
  is_required: boolean
}

// 培养方案API
export const curriculumAPI = {
  // 获取所有课程标签
  getCourseTags: (): Promise<CourseTag[]> => {
    return api.get('/curriculum/tags/');
  },

  // 获取培养方案模板
  getTemplates: (): Promise<CurriculumTemplate[]> => {
    return api.get('/curriculum/templates/')
  },

  // 获取培养方案详情
  getTemplateDetail: (id: number): Promise<CurriculumTemplate> => {
    return api.get(`/curriculum/templates/${id}/`)
  },

  // 获取培养方案的课程
  getTemplateCourses: (templateId: number): Promise<TemplateCourse[]> => {
    return api.get(`/curriculum/templates/${templateId}/template_courses/`)
  },

  // 获取所有模板课程（可按模板ID过滤）
  getAllTemplateCourses: (templateId?: number): Promise<TemplateCourse[]> => {
    const params = templateId ? `?template_id=${templateId}` : ''
    return api.get(`/curriculum/template-courses/${params}`)
  },

  // 毕业预审API
  getGraduationAudit: (): Promise<FullAuditReport> => {
    return api.get('/curriculum/graduation-audit/audit/')
  },

  // 激活一个培养方案
  activateTemplate: (templateId: number): Promise<any> => {
    return api.post(`/curriculum/templates/${templateId}/activate/`)
  },

  // 从JSON对象创建培养方案
  createTemplateFromJson: (data: any): Promise<CurriculumTemplate> => {
    return api.post('/curriculum/templates/create-from-json/', data);
  }
}

export default curriculumAPI
