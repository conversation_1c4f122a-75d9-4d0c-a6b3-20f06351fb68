from django.db import models
from django.contrib.auth import get_user_model
from courses.models import Course, CourseTag

User = get_user_model()

class CurriculumTemplate(models.Model):
    """培养方案模板"""
    school = models.CharField(max_length=100, verbose_name='学校')
    major = models.CharField(max_length=100, verbose_name='专业')
    enrollment_year = models.CharField(max_length=10, verbose_name='适用年份')
    total_credits_required = models.IntegerField(verbose_name='总学分要求')
    description = models.TextField(blank=True, verbose_name='方案描述')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '培养方案模板'
        verbose_name_plural = '培养方案模板'
        db_table = 'curriculum_templates'
        unique_together = ['school', 'major', 'enrollment_year']

    def __str__(self):
        return f"{self.school} - {self.major} ({self.enrollment_year})"

class RequirementModule(models.Model):
    """要求模块，代表一个毕业要求，例如'专业选修课'或'通识教育-人文社科'"""
    template = models.ForeignKey(CurriculumTemplate, on_delete=models.CASCADE, related_name='modules', verbose_name='培养方案模板')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children', verbose_name='父模块')
    
    name = models.CharField(max_length=100, verbose_name='要求模块名称')
    description = models.TextField(blank=True, verbose_name='模块描述')
    
    # 主要目标：可以是学分，也可以是课程数
    credits_required = models.PositiveIntegerField(default=0, verbose_name='总学分要求')
    courses_required = models.PositiveIntegerField(default=0, verbose_name='总课程数要求')

    class Meta:
        verbose_name = '要求模块'
        verbose_name_plural = '要求模块'
        db_table = 'requirement_modules'
        unique_together = ['template', 'name']

    def __str__(self):
        return f"{self.template.major} - {self.name}"

class Rule(models.Model):
    """规则，用于定义一个模块内的具体要求，如课程池或子要求"""
    module = models.ForeignKey(RequirementModule, on_delete=models.CASCADE, related_name='rules', verbose_name='所属模块')
    name = models.CharField(max_length=100, verbose_name='规则名称') # e.g., "主课程池", "实践课要求"
    description = models.TextField(blank=True, verbose_name='规则描述')

    # 规则的约束目标
    credits_required = models.PositiveIntegerField(default=0, verbose_name='要求学分')
    courses_required = models.PositiveIntegerField(default=0, verbose_name='要求课程数')

    # 规则的课程池定义
    tags = models.ManyToManyField(CourseTag, verbose_name='所需课程标签')
    
    TAG_MATCH_CHOICES = [
        ('ANY', '满足任一标签'),
        ('ALL', '满足所有标签'),
    ]
    tag_match_logic = models.CharField(
        max_length=3, 
        choices=TAG_MATCH_CHOICES, 
        default='ANY', 
        verbose_name='标签匹配逻辑'
    )

    class Meta:
        verbose_name = '规则'
        verbose_name_plural = '规则'
        db_table = 'rules'

    def __str__(self):
        return f"{self.module.name} - {self.name}"

class TemplateCourse(models.Model):
    """模板课程"""
    template = models.ForeignKey(CurriculumTemplate, on_delete=models.CASCADE, verbose_name='培养方案模板')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, verbose_name='课程')
    recommended_semester = models.IntegerField(verbose_name='推荐学期')
    is_required = models.BooleanField(default=True, verbose_name='是否必修')

    class Meta:
        verbose_name = '模板课程'
        verbose_name_plural = '模板课程'
        db_table = 'template_courses'
        unique_together = ['template', 'course']

    def __str__(self):
        return f"{self.template} - {self.course}"

class UserCurriculumTemplate(models.Model):
    """用户选择的培养方案"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    template = models.ForeignKey(CurriculumTemplate, on_delete=models.CASCADE, verbose_name='培养方案模板')
    is_active = models.BooleanField(default=True, verbose_name='是否当前使用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '用户培养方案'
        verbose_name_plural = '用户培养方案'
        db_table = 'user_curriculum_templates'

    def __str__(self):
        return f"{self.user.username} - {self.template}"

class UserCreditRequirement(models.Model):
    """用户的自定义学分要求"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='custom_credit_requirements', verbose_name='用户')
    template = models.ForeignKey(CurriculumTemplate, on_delete=models.CASCADE, related_name='user_customizations', verbose_name='培养方案模板')
    category = models.CharField(max_length=100, verbose_name='课程类别')
    required_credits = models.PositiveIntegerField(verbose_name='要求学分')
    description = models.TextField(blank=True, null=True, verbose_name='要求描述')
    
    # original_requirement = models.ForeignKey(CreditRequirement, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='被覆盖的原始要求')

    class Meta:
        verbose_name = '用户自定义学分要求'
        verbose_name_plural = '用户自定义学分要求'
        db_table = 'user_credit_requirements'
        unique_together = ('user', 'template', 'category')

    def __str__(self):
        return f"自定义要求: {self.user.username} - {self.category} ({self.required_credits}学分)"
