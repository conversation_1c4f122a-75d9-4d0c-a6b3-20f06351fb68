<template>
  <div class="profile">
    <div class="profile-header">
      <h1>个人信息</h1>
      <p>管理您的个人资料、偏好设置和账户安全</p>
    </div>

    <el-row :gutter="24">
      <!-- Left Column: Avatar and Quick Actions -->
      <el-col :xs="24" :sm="8">
        <el-card class="profile-card">
          <div class="avatar-section">
            <el-upload
              class="avatar-uploader"
              action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <el-avatar :size="120" :src="userInfo.avatar || undefined">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="avatar-edit-overlay">
                <el-icon><Edit /></el-icon>
                <span>更换头像</span>
              </div>
            </el-upload>
            <h2 class="user-display-name">{{ userInfo.real_name || userInfo.username }}</h2>
            <p class="user-email">{{ userInfo.email }}</p>
          </div>
          
          <el-divider />

          <div class="quick-links">
            <div class="quick-link-item">
              <el-icon><Lock /></el-icon>
              <span>账户安全</span>
              <el-button text bg @click="scrollTo('security-card')">前往</el-button>
            </div>
            <div class="quick-link-item">
              <el-icon><Bell /></el-icon>
              <span>通知设置</span>
              <el-button text bg @click="ElMessage.info('功能开发中')">前往</el-button>
            </div>
            <div class="quick-link-item">
              <el-icon><Brush /></el-icon>
              <span>外观偏好</span>
              <el-button text bg @click="ElMessage.info('功能开发中')">前往</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- Right Column: Info Forms -->
      <el-col :xs="24" :sm="16">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <el-button 
                :type="editMode ? 'default' : 'primary'"
                :icon="editMode ? Close : Edit"
                circle
                @click="toggleEditMode" 
              />
            </div>
          </template>
          
          <el-form
            ref="profileFormRef"
            :model="userInfo"
            :rules="profileRules"
            label-position="top"
            :disabled="!editMode"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="userInfo.username" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="userInfo.email" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="真实姓名" prop="real_name">
                  <el-input v-model="userInfo.real_name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="学号" prop="student_id">
                  <el-input v-model="userInfo.student_id" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="学校" prop="school">
                  <el-input v-model="userInfo.school" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="学院" prop="college">
                  <el-input v-model="userInfo.college" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="专业" prop="major">
                  <el-input v-model="userInfo.major" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="入学年份" prop="enrollment_year">
                  <el-date-picker
                    v-model="enrollmentYearDate"
                    type="year"
                    placeholder="选择入学年份"
                    style="width: 100%"
                    format="YYYY"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="个人简介" prop="bio">
              <el-input
                v-model="userInfo.bio"
                type="textarea"
                :rows="3"
                placeholder="介绍一下自己吧"
              />
            </el-form-item>
            
            <el-form-item v-if="editMode">
              <el-button type="primary" @click="saveProfile" :loading="isSaving">保存更改</el-button>
              <el-button @click="cancelEdit">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="security-card" id="security-card">
          <template #header>
            <span>账户安全</span>
          </template>
          <div class="security-items">
            <div class="security-item">
              <div class="security-info">
                <el-icon><Lock /></el-icon>
                <div class="security-details">
                  <span class="security-title">登录密码</span>
                  <span class="security-desc">建议定期更换密码以保护账户安全</span>
                </div>
              </div>
              <el-button text @click="showChangePasswordDialog">修改</el-button>
            </div>
            <div class="security-item">
              <el-icon><Message /></el-icon>
              <div class="security-details">
                <span class="security-title">邮箱验证</span>
                <span class="security-desc">{{ userInfo.email_verified ? '已验证' : '邮箱未验证，部分功能可能受限' }}</span>
              </div>
              <el-button v-if="!userInfo.email_verified" type="primary" plain>发送验证邮件</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="450px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-position="top"
        style="padding: 0 10px;"
      >
        <el-form-item label="当前密码" prop="old_password">
          <el-input
            v-model="passwordForm.old_password"
            type="password"
            show-password
            placeholder="请输入您当前的密码"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="new_password">
          <el-input
            v-model="passwordForm.new_password"
            type="password"
            show-password
            placeholder="请输入6-20位的新密码"
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirm_password">
          <el-input
            v-model="passwordForm.confirm_password"
            type="password"
            show-password
            placeholder="请再次输入新密码"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="changePassword" :loading="isSavingPassword">
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type UploadProps } from 'element-plus'
import { User, Edit, Lock, Bell, Brush, Close, Message } from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'
import { authAPI } from '../services/auth'

const userStore = useUserStore()
const editMode = ref(false)
const isSaving = ref(false)
const passwordDialogVisible = ref(false)
const isSavingPassword = ref(false)
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

const userInfo = reactive({
  username: '',
  email: '',
  real_name: '',
  student_id: '',
  school: '',
  college: '',
  major: '',
  enrollment_year: null as number | null,
  bio: '',
  avatar: '',
  email_verified: false,
})

let originalUserInfo = {}

// 用于 el-date-picker 的中间变量
const enrollmentYearDate = computed({
  get: () => userInfo.enrollment_year ? new Date(String(userInfo.enrollment_year)) : null,
  set: (val) => {
    userInfo.enrollment_year = val ? val.getFullYear() : null
  }
})

const profileRules = {
  real_name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  student_id: [{ required: true, message: '请输入学号', trigger: 'blur' }],
  school: [{ required: true, message: '请输入学校名称', trigger: 'blur' }],
  college: [{ required: true, message: '请输入学院名称', trigger: 'blur' }],
  major: [{ required: true, message: '请输入专业名称', trigger: 'blur' }],
  enrollment_year: [{ required: true, message: '请选择入学年份', trigger: 'change' }]
}

const toggleEditMode = () => {
  editMode.value = !editMode.value
  if (editMode.value) {
    // 进入编辑模式时，备份原始数据
    originalUserInfo = { ...userInfo }
  } else {
    // 退出编辑模式时，恢复原始数据
    Object.assign(userInfo, originalUserInfo)
  }
}

const cancelEdit = () => {
  Object.assign(userInfo, originalUserInfo)
  editMode.value = false
}

const saveProfile = async () => {
  if (!profileFormRef.value) return
  isSaving.value = true
  await profileFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await userStore.updateProfile(userInfo)
        editMode.value = false
        ElMessage.success('个人信息更新成功')
      } catch (error) {
        ElMessage.error('更新失败，请重试')
      } finally {
        isSaving.value = false
      }
    } else {
      isSaving.value = false
    }
  })
}

const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
  // 假设后端返回的格式是 { url: '...' }
  const newAvatarUrl = response.url
  userInfo.avatar = newAvatarUrl
  // 调用API更新用户头像
  userStore.updateProfile({ avatar: newAvatarUrl }).then(() => {
    ElMessage.success('头像更新成功')
  }).catch(() => {
    ElMessage.error('头像更新失败')
  })
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (!rawFile.type.startsWith('image/')) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const scrollTo = (elementId: string) => {
  document.getElementById(elementId)?.scrollIntoView({ behavior: 'smooth' })
}

const passwordForm = reactive({
  old_password: '',
  new_password: '',
  confirm_password: ''
})

const validateNewPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入新密码'))
  } else if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== passwordForm.new_password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const passwordRules = {
  old_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, validator: validateNewPassword, trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const showChangePasswordDialog = () => {
  passwordDialogVisible.value = true
}

const changePassword = async () => {
  if (!passwordFormRef.value) return
  isSavingPassword.value = true
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await authAPI.changePassword({
          old_password: passwordForm.old_password,
          new_password: passwordForm.new_password
        })
        passwordDialogVisible.value = false
        ElMessage.success('密码修改成功，请重新登录')
        // 密码修改成功后，强制用户重新登录
        userStore.logout()
        router.push('/login')
      } catch (error: any) {
        const errorMessage = error.response?.data?.old_password?.[0] || '密码修改失败，请检查当前密码是否正确'
        ElMessage.error(errorMessage)
      } finally {
        isSavingPassword.value = false
      }
    } else {
      isSavingPassword.value = false
    }
  })
}

onMounted(() => {
  if (userStore.user) {
    Object.assign(userInfo, userStore.user)
    originalUserInfo = { ...userInfo }
  } else {
    // 如果store中没有用户信息，尝试从服务器获取
    userStore.refreshProfile().then(() => {
      if(userStore.user) {
        Object.assign(userInfo, userStore.user)
        originalUserInfo = { ...userInfo }
      }
    })
  }
})
</script>

<style scoped>
/* 样式重构 */
.profile {
  padding: 24px;
}

.profile-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
}

.profile-header p {
  font-size: 16px;
  color: #718096;
  margin-top: 4px;
  margin-bottom: 24px;
}

.profile-card, .info-card, .security-card {
  height: 100%;
}

.security-card {
  margin-top: 24px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0;
}

.avatar-uploader {
  position: relative;
  cursor: pointer;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 16px;
}

.avatar-uploader:hover .avatar-edit-overlay {
  opacity: 1;
}

.avatar-edit-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.user-display-name {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.user-email {
  font-size: 14px;
  color: #718096;
}

.quick-links {
  padding: 8px;
}

.quick-link-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.quick-link-item:hover {
  background-color: #f9fafb;
}

.quick-link-item .el-icon {
  margin-right: 12px;
  color: #a0aec0;
}

.quick-link-item span {
  flex: 1;
  font-size: 14px;
  color: #4a5568;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.security-items {
  padding: 8px 0;
}

.security-item {
  display: flex;
  align-items: center;
  padding: 16px 8px;
  border-bottom: 1px solid #f1f5f9;
}

.security-item:last-child {
  border-bottom: none;
}

.security-item .el-icon {
  font-size: 20px;
  color: #a0aec0;
  margin-right: 16px;
}

.security-details {
  flex: 1;
}

.security-title {
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 2px;
}

.security-desc {
  font-size: 13px;
  color: #718096;
}

/* 修改密码对话框 */
.dialog-footer {
  text-align: right;
}
</style>
