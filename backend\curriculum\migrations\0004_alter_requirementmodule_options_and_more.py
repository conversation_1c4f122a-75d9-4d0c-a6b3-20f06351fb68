# Generated by Django 5.2.3 on 2025-06-24 16:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0005_remove_course_credits_course_practical_credits_and_more'),
        ('curriculum', '0003_remove_templatecourse_category_requirementmodule_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='requirementmodule',
            options={'ordering': ['order', 'name'], 'verbose_name': '要求模块', 'verbose_name_plural': '要求模块'},
        ),
        migrations.RenameField(
            model_name='requirementmodule',
            old_name='credits_required',
            new_name='total_credits_required',
        ),
        migrations.RemoveField(
            model_name='rule',
            name='credits_required',
        ),
        migrations.AddField(
            model_name='requirementmodule',
            name='course_category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='courses.coursecategory', verbose_name='对应课程分类'),
        ),
        migrations.AddField(
            model_name='requirementmodule',
            name='order',
            field=models.PositiveIntegerField(default=0, verbose_name='排序'),
        ),
        migrations.AddField(
            model_name='requirementmodule',
            name='practical_credits_required',
            field=models.PositiveIntegerField(default=0, verbose_name='实践学分要求'),
        ),
        migrations.AddField(
            model_name='rule',
            name='category_match_logic',
            field=models.CharField(choices=[('ANY', '满足任一分类'), ('ALL', '满足所有分类')], default='ANY', max_length=3, verbose_name='分类匹配逻辑'),
        ),
        migrations.AddField(
            model_name='rule',
            name='course_categories',
            field=models.ManyToManyField(blank=True, to='courses.coursecategory', verbose_name='所需课程分类'),
        ),
        migrations.AddField(
            model_name='rule',
            name='practical_credits_required',
            field=models.PositiveIntegerField(default=0, verbose_name='实践学分要求'),
        ),
        migrations.AddField(
            model_name='rule',
            name='total_credits_required',
            field=models.PositiveIntegerField(default=0, verbose_name='总学分要求'),
        ),
        migrations.AlterField(
            model_name='rule',
            name='tags',
            field=models.ManyToManyField(blank=True, to='courses.coursetag', verbose_name='所需课程标签'),
        ),
    ]
