<template>
  <div id="app">
    <el-container v-if="!isAuthPage" class="main-container">
      <el-header class="app-header-container">
        <AppHeader />
      </el-header>
      <el-container class="content-container">
        <el-aside width="280px" class="sidebar-container">
          <AppSidebar />
        </el-aside>
        <el-main class="main-content">
          <div class="page-wrapper">
            <transition name="page-fade" mode="out-in">
              <router-view />
            </transition>
          </div>
        </el-main>
      </el-container>
    </el-container>
    <div v-else class="auth-container">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import AppHeader from './components/AppHeader.vue'
import AppSidebar from './components/AppSidebar.vue'
import { useUserStore } from './stores/user'

const route = useRoute()
const userStore = useUserStore()

const isAuthPage = computed(() => {
  return route.path === '/login' || route.path === '/register'
})

onMounted(() => {
  // 初始化用户信息
  userStore.initUserFromStorage()
})
</script>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f8fafc;
  color: #334155;
  line-height: 1.6;
}

#app {
  height: 100vh;
  overflow: visible;
  position: relative;
}

/* 主容器布局 */
.main-container {
  height: 100vh;
  background: #f8fafc;
}

.app-header-container {
  padding: 0;
  height: 64px;
  position: relative;
  z-index: 1000;
}

.content-container {
  height: calc(100vh - 64px);
  overflow: visible;
  position: relative;
}

.sidebar-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  transition: width 0.3s ease;
}

.main-content {
  padding: 0;
  background: #f8fafc;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}

.page-wrapper {
  min-height: calc(100vh - 64px - 48px); /* 减去header高度和padding */
  padding: 24px;
  box-sizing: border-box;
}

/* 页面切换动画 */
.page-fade-enter-active,
.page-fade-leave-active {
  transition: all 0.3s ease;
}

.page-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 认证页面容器 */
.auth-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Element Plus 组件样式覆盖 */
.el-header {
  padding: 0;
  border-bottom: 1px solid #e2e8f0;
}

.el-aside {
  border-right: 1px solid #e2e8f0;
  background: #fafbfc;
}

.el-main {
  padding: 0;
}

/* 卡片组件统一样式 */
.el-card {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.el-card__header {
  background: #fafbfc;
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 20px;
  font-weight: 600;
  color: #334155;
}

.el-card__body {
  padding: 20px;
}

/* 按钮统一样式 */
.el-button--primary {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.el-button--primary:hover {
  background: linear-gradient(90deg, #5a67d8 0%, #6b46c1 100%);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

/* 表格样式 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background: #f8fafc;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar-container {
    width: 240px !important;
  }
  
  .page-wrapper {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .sidebar-container {
    width: 0 !important;
    overflow: visible;
  }
  
  .page-wrapper {
    padding: 16px;
  }
  
  .main-content {
    width: 100%;
  }
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #64748b;
}

.empty-state .empty-icon {
  font-size: 48px;
  color: #cbd5e1;
  margin-bottom: 16px;
}

.empty-state .empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-state .empty-desc {
  font-size: 14px;
  color: #94a3b8;
}
</style>
