<template>
  <div class="app-sidebar">
    <div class="sidebar-content">
      <!-- 用户信息卡片 -->
      <div class="user-card">
        <el-avatar 
          :size="48" 
          :src="userStore.user?.avatar || undefined"
          class="user-avatar"
        >
          <el-icon><User /></el-icon>
        </el-avatar>
        <div class="user-info">
          <div class="user-name">{{ displayName }}</div>
          <div class="user-school">{{ userStore.user?.school || '学校未设置' }}</div>
        </div>
      </div>

      <!-- 导航菜单 -->
      <el-menu
        :default-active="activeIndex"
        class="sidebar-menu"
        router
        @select="handleSelect"
      >
        <el-menu-item index="/dashboard" class="menu-item">
          <div class="menu-item-content">
            <el-icon class="menu-icon"><DataBoard /></el-icon>
            <span class="menu-text">进度仪表盘</span>
          </div>
        </el-menu-item>
        
        <el-menu-item index="/journey" class="menu-item">
          <div class="menu-item-content">
            <el-icon class="menu-icon"><List /></el-icon>
            <span class="menu-text">我的学途</span>
          </div>
        </el-menu-item>
        
        <el-menu-item index="/curriculum" class="menu-item">
          <div class="menu-item-content">
            <el-icon class="menu-icon"><Document /></el-icon>
            <span class="menu-text">培养方案</span>
          </div>
        </el-menu-item>
        
        <el-menu-item index="/audit" class="menu-item">
          <div class="menu-item-content">
            <el-icon class="menu-icon"><Check /></el-icon>
            <span class="menu-text">毕业预审</span>
          </div>
        </el-menu-item>
      </el-menu>

      <!-- 底部信息 -->
      <div class="sidebar-footer">
        <div class="quick-stats">
          <div class="stat-item">
            <div class="stat-value">{{ courseStore.totalCredits }}</div>
            <div class="stat-label">已修学分</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ courseStore.gpa.toFixed(2) }}</div>
            <div class="stat-label">当前GPA</div>
          </div>
        </div>
        
        <div class="progress-indicator">
          <div class="progress-text">毕业进度</div>
          <el-progress 
            :percentage="graduationProgress" 
            :stroke-width="6"
            :show-text="false"
            color="#667eea"
          />
          <div class="progress-percentage">{{ graduationProgress }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { DataBoard, List, Document, Check, User } from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'
import { useCourseStore } from '../stores/course'

const route = useRoute()
const userStore = useUserStore()
const courseStore = useCourseStore()

const activeIndex = computed(() => route.path)

const displayName = computed(() => {
  return userStore.user?.real_name || userStore.user?.username || '未登录'
})

const graduationProgress = computed(() => {
  const totalRequired = 151 // 总毕业学分要求
  return Math.min(Math.round((courseStore.totalCredits / totalRequired) * 100), 100)
})

const handleSelect = (key: string) => {
  console.log('Selected menu:', key)
}

onMounted(() => {
  // 确保课程数据已加载
  if (courseStore.userCourses.length === 0) {
    courseStore.fetchUserCourses()
  }
})
</script>

<style scoped>
.app-sidebar {
  height: 100%;
  background: #fafbfc;
  border-right: 1px solid #e8ebef;
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px 0;
}

/* 用户信息卡片 */
.user-card {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  margin: 0 16px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e8ebef;
}

.user-avatar {
  margin-right: 12px;
  border: 2px solid #f0f2f5;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.user-school {
  font-size: 12px;
  color: #8492a6;
}

/* 导航菜单 */
.sidebar-menu {
  border-right: none;
  background: transparent;
  flex: 1;
  padding: 0 16px;
}

.menu-item {
  margin-bottom: 4px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.menu-item-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 4px 0;
}

.menu-icon {
  font-size: 18px;
  margin-right: 12px;
  color: #64748b;
  transition: color 0.3s ease;
}

.menu-text {
  font-size: 14px;
  font-weight: 500;
  color: #475569;
  transition: color 0.3s ease;
}

/* 菜单项状态 */
.sidebar-menu .el-menu-item {
  height: 48px;
  line-height: 48px;
  margin: 0;
  border-radius: 8px;
  background: transparent;
  border: none;
  transition: all 0.3s ease;
}

.sidebar-menu .el-menu-item:hover {
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.sidebar-menu .el-menu-item:hover .menu-icon {
  color: #667eea;
}

.sidebar-menu .el-menu-item:hover .menu-text {
  color: #667eea;
}

.sidebar-menu .el-menu-item.is-active {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.sidebar-menu .el-menu-item.is-active .menu-icon,
.sidebar-menu .el-menu-item.is-active .menu-text {
  color: white;
}

/* 底部信息 */
.sidebar-footer {
  padding: 16px 20px;
  margin-top: auto;
}

.quick-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e8ebef;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 11px;
  color: #8492a6;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.progress-indicator {
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e8ebef;
}

.progress-text {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 500;
}

.progress-percentage {
  font-size: 11px;
  color: #667eea;
  text-align: right;
  margin-top: 4px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sidebar {
    width: 100% !important;
    position: fixed;
    top: 64px;
    left: 0;
    bottom: 0;
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .app-sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .user-card {
    margin: 0 12px 20px;
    padding: 12px 16px;
  }
  
  .sidebar-footer {
    padding: 12px 16px;
  }
  
  .quick-stats {
    padding: 8px;
  }
  
  .progress-indicator {
    padding: 8px;
  }
}
</style>
