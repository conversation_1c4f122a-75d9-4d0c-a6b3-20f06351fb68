from django.contrib.auth.models import AbstractUser, Group, Permission
from django.db import models

class User(AbstractUser):
    """自定义用户模型"""
    email = models.EmailField(unique=True, verbose_name='邮箱')
    real_name = models.CharField(max_length=50, blank=True, verbose_name='真实姓名')
    student_id = models.CharField(max_length=20, blank=True, unique=True, null=True, verbose_name='学号')
    school = models.CharField(max_length=100, blank=True, verbose_name='学校')
    college = models.CharField(max_length=100, blank=True, verbose_name='学院')
    major = models.CharField(max_length=100, blank=True, verbose_name='专业')
    enrollment_year = models.IntegerField(null=True, blank=True, verbose_name='入学年份')
    bio = models.TextField(blank=True, verbose_name='个人简介')
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True, verbose_name='头像')
    email_verified = models.BooleanField(default=False, verbose_name='邮箱已验证')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    groups = models.ManyToManyField(
        Group,
        related_name='custom_user_set',
        blank=True,
        verbose_name='用户组',
        help_text='用户所属的组',
    )
    user_permissions = models.ManyToManyField(
        Permission,
        related_name='custom_user_set',
        blank=True,
        verbose_name='用户权限',
        help_text='用户拥有的权限',
    )

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'
        db_table = 'users'

    def __str__(self):
        return self.email
