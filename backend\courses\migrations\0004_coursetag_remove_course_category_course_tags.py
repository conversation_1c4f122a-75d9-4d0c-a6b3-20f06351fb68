# Generated by Django 5.2.3 on 2025-06-24 13:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0003_alter_usercourse_grade'),
    ]

    operations = [
        migrations.CreateModel(
            name='CourseTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='标签名称')),
                ('description', models.Char<PERSON>ield(blank=True, max_length=200, verbose_name='描述')),
            ],
            options={
                'verbose_name': '课程标签',
                'verbose_name_plural': '课程标签',
                'db_table': 'course_tags',
            },
        ),
        migrations.RemoveField(
            model_name='course',
            name='category',
        ),
        migrations.AddField(
            model_name='course',
            name='tags',
            field=models.ManyToManyField(blank=True, to='courses.coursetag', verbose_name='课程标签'),
        ),
    ]
