#!/usr/bin/env python3
"""
设置新的课程分类系统
根据您的需求创建课程分类、示例课程和学分要求
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'academic_journey.settings')
django.setup()

from courses.models import CourseCategory, Course, CourseTag
from curriculum.models import CurriculumTemplate, RequirementModule, Rule
from django.contrib.auth import get_user_model

User = get_user_model()

def create_course_categories():
    """创建课程分类体系"""
    print("🏗️ 创建课程分类体系...")

    # 清除现有分类
    CourseCategory.objects.all().delete()

    # 创建主要分类
    categories = []

    # 1. 学科基础类
    subject_basic = CourseCategory.objects.create(
        name="学科基础类",
        description="学科基础课程，为专业学习打下基础",
        total_credits_required=0,  # 具体要求在培养方案中设置
        practical_credits_required=0,
        order=1
    )
    categories.append(subject_basic)
    
    # 2. 专业必修类
    major_required = CourseCategory.objects.create(
        name="专业必修类",
        description="专业核心必修课程",
        total_credits_required=0,
        practical_credits_required=0,
        order=2
    )
    categories.append(major_required)
    
    # 3. 专业任选类
    major_elective = CourseCategory.objects.create(
        name="专业任选类",
        description="专业选修课程，至少8学分，其中实践3学分",
        total_credits_required=8,
        practical_credits_required=3,
        order=3
    )
    categories.append(major_elective)
    
    # 4. 公共任选类（主分类）
    public_elective = CourseCategory.objects.create(
        name="公共任选类",
        description="公共选修课程，包含四个子类别",
        total_credits_required=10,  # 2+2+2+4=10学分
        practical_credits_required=0,
        order=4
    )
    categories.append(public_elective)
    
    # 4.1 自然科学类
    natural_science = CourseCategory.objects.create(
        name="自然科学类",
        parent=public_elective,
        description="自然科学相关课程",
        total_credits_required=2,
        practical_credits_required=0,
        order=1
    )
    categories.append(natural_science)
    
    # 4.2 人文社科类
    humanities = CourseCategory.objects.create(
        name="人文社科类",
        parent=public_elective,
        description="人文社会科学相关课程",
        total_credits_required=2,
        practical_credits_required=0,
        order=2
    )
    categories.append(humanities)
    
    # 4.3 艺术审美类
    arts = CourseCategory.objects.create(
        name="艺术审美类",
        parent=public_elective,
        description="艺术审美相关课程",
        total_credits_required=2,
        practical_credits_required=0,
        order=3
    )
    categories.append(arts)
    
    # 4.4 校本特色类
    school_special = CourseCategory.objects.create(
        name="校本特色类",
        parent=public_elective,
        description="学校特色课程",
        total_credits_required=4,
        practical_credits_required=0,
        order=4
    )
    categories.append(school_special)
    
    print(f"✅ 创建了 {len(categories)} 个课程分类")
    return categories

def create_sample_courses():
    """创建示例课程"""
    print("\n📚 创建示例课程...")
    
    # 获取分类
    subject_basic = CourseCategory.objects.get(name="学科基础类")
    major_required = CourseCategory.objects.get(name="专业必修类")
    major_elective = CourseCategory.objects.get(name="专业任选类")
    natural_science = CourseCategory.objects.get(name="自然科学类")
    humanities = CourseCategory.objects.get(name="人文社科类")
    arts = CourseCategory.objects.get(name="艺术审美类")
    school_special = CourseCategory.objects.get(name="校本特色类")
    
    courses = []
    
    # 学科基础类课程
    course_data = [
        {
            "course_code": "MATH101",
            "course_name": "高等数学",
            "theoretical_credits": 4,
            "practical_credits": 0,
            "category": subject_basic,
            "description": "数学基础课程"
        },
        {
            "course_code": "CS101",
            "course_name": "程序设计基础",
            "theoretical_credits": 2,
            "practical_credits": 2,
            "category": subject_basic,
            "description": "编程入门课程"
        },
    ]

    for data in course_data:
        course, created = Course.objects.get_or_create(
            course_code=data["course_code"],
            defaults=data
        )
        if created:
            courses.append(course)
        else:
            # 更新现有课程的分类和学分信息
            for key, value in data.items():
                if key != "course_code":
                    setattr(course, key, value)
            course.save()
            courses.append(course)
    
    # 专业必修类课程
    major_required_data = [
        {
            "course_code": "CS201",
            "course_name": "数据结构与算法",
            "theoretical_credits": 3,
            "practical_credits": 1,
            "category": major_required,
            "description": "计算机专业核心课程"
        },
        {
            "course_code": "CS202",
            "course_name": "操作系统",
            "theoretical_credits": 3,
            "practical_credits": 1,
            "category": major_required,
            "description": "系统软件基础"
        },
    ]

    for data in major_required_data:
        course, created = Course.objects.get_or_create(
            course_code=data["course_code"],
            defaults=data
        )
        if not created:
            for key, value in data.items():
                if key != "course_code":
                    setattr(course, key, value)
            course.save()
        courses.append(course)

    # 专业任选类课程
    major_elective_data = [
        {
            "course_code": "CS301",
            "course_name": "图像创意设计",
            "theoretical_credits": 2,
            "practical_credits": 1,
            "category": major_elective,
            "description": "创意设计课程，理论2学分，实践1学分"
        },
        {
            "course_code": "CS302",
            "course_name": "人工智能导论",
            "theoretical_credits": 2,
            "practical_credits": 1,
            "category": major_elective,
            "description": "AI基础课程"
        },
    ]

    for data in major_elective_data:
        course, created = Course.objects.get_or_create(
            course_code=data["course_code"],
            defaults=data
        )
        if not created:
            for key, value in data.items():
                if key != "course_code":
                    setattr(course, key, value)
            course.save()
        courses.append(course)

    # 公共任选类课程
    public_elective_data = [
        {
            "course_code": "NS101",
            "course_name": "环境科学概论",
            "theoretical_credits": 2,
            "practical_credits": 0,
            "category": natural_science,
            "description": "自然科学类选修课"
        },
        {
            "course_code": "HU101",
            "course_name": "中国传统文化",
            "theoretical_credits": 2,
            "practical_credits": 0,
            "category": humanities,
            "description": "人文社科类选修课"
        },
        {
            "course_code": "AR101",
            "course_name": "音乐欣赏",
            "theoretical_credits": 2,
            "practical_credits": 0,
            "category": arts,
            "description": "艺术审美类选修课"
        },
        {
            "course_code": "SC101",
            "course_name": "创新创业实践",
            "theoretical_credits": 2,
            "practical_credits": 2,
            "category": school_special,
            "description": "校本特色类课程"
        },
    ]

    for data in public_elective_data:
        course, created = Course.objects.get_or_create(
            course_code=data["course_code"],
            defaults=data
        )
        if not created:
            for key, value in data.items():
                if key != "course_code":
                    setattr(course, key, value)
            course.save()
        courses.append(course)
    
    print(f"✅ 创建了 {len(courses)} 门示例课程")
    return courses

def setup_curriculum_requirements():
    """设置培养方案要求"""
    print("\n🎯 设置培养方案要求...")
    
    # 获取或创建培养方案模板
    template, created = CurriculumTemplate.objects.get_or_create(
        school="某某大学",
        major="计算机科学与技术",
        enrollment_year="2024",
        defaults={
            'total_credits_required': 151,
            'description': '计算机科学与技术专业培养方案（新分类系统）'
        }
    )
    
    if created:
        print(f"✅ 创建培养方案模板: {template}")
    else:
        print(f"✅ 使用现有培养方案模板: {template}")
    
    # 清除旧的要求模块
    RequirementModule.objects.filter(template=template).delete()
    
    # 创建要求模块
    modules = []
    
    # 获取分类
    subject_basic = CourseCategory.objects.get(name="学科基础类")
    major_required = CourseCategory.objects.get(name="专业必修类")
    major_elective = CourseCategory.objects.get(name="专业任选类")
    public_elective = CourseCategory.objects.get(name="公共任选类")
    
    # 学科基础要求
    modules.append(RequirementModule.objects.create(
        template=template,
        name="学科基础要求",
        description="学科基础类课程要求",
        course_category=subject_basic,
        total_credits_required=20,
        practical_credits_required=5,
        order=1
    ))
    
    # 专业必修要求
    modules.append(RequirementModule.objects.create(
        template=template,
        name="专业必修要求",
        description="专业必修类课程要求",
        course_category=major_required,
        total_credits_required=40,
        practical_credits_required=10,
        order=2
    ))
    
    # 专业任选要求
    modules.append(RequirementModule.objects.create(
        template=template,
        name="专业任选要求",
        description="专业任选类课程要求，至少8学分，其中实践3学分",
        course_category=major_elective,
        total_credits_required=8,
        practical_credits_required=3,
        order=3
    ))
    
    # 公共任选要求
    public_module = RequirementModule.objects.create(
        template=template,
        name="公共任选要求",
        description="公共任选类课程要求，总计10学分",
        course_category=public_elective,
        total_credits_required=10,
        practical_credits_required=0,
        order=4
    )
    modules.append(public_module)
    
    # 公共任选子要求
    for sub_category in public_elective.children.all():
        modules.append(RequirementModule.objects.create(
            template=template,
            parent=public_module,
            name=f"{sub_category.name}要求",
            description=f"{sub_category.name}课程要求",
            course_category=sub_category,
            total_credits_required=sub_category.total_credits_required,
            practical_credits_required=sub_category.practical_credits_required,
            order=sub_category.order
        ))
    
    print(f"✅ 创建了 {len(modules)} 个要求模块")
    return modules

def main():
    """主函数"""
    print("🚀 开始设置新的课程分类系统...")
    
    try:
        # 1. 创建课程分类
        categories = create_course_categories()
        
        # 2. 创建示例课程
        courses = create_sample_courses()
        
        # 3. 设置培养方案要求
        modules = setup_curriculum_requirements()
        
        print("\n🎉 新的课程分类系统设置完成！")
        print("\n📊 系统概览:")
        print(f"  - 课程分类: {len(categories)} 个")
        print(f"  - 示例课程: {len(courses)} 门")
        print(f"  - 要求模块: {len(modules)} 个")
        
        print("\n📋 分类结构:")
        for category in CourseCategory.objects.filter(parent__isnull=True).order_by('order'):
            print(f"  {category.name} (总学分要求: {category.total_credits_required}, 实践学分要求: {category.practical_credits_required})")
            for child in category.children.all().order_by('order'):
                print(f"    └─ {child.name} (总学分要求: {child.total_credits_required}, 实践学分要求: {child.practical_credits_required})")
        
    except Exception as e:
        print(f"❌ 设置过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
