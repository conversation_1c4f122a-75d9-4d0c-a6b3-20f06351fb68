# Generated by Django 5.2.3 on 2025-06-24 16:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0004_coursetag_remove_course_category_course_tags'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='course',
            name='credits',
        ),
        migrations.AddField(
            model_name='course',
            name='practical_credits',
            field=models.PositiveIntegerField(default=0, verbose_name='实践学分'),
        ),
        migrations.AddField(
            model_name='course',
            name='theoretical_credits',
            field=models.PositiveIntegerField(default=0, verbose_name='理论学分'),
        ),
        migrations.CreateModel(
            name='CourseCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='分类名称')),
                ('description', models.TextField(blank=True, verbose_name='分类描述')),
                ('total_credits_required', models.PositiveIntegerField(default=0, verbose_name='总学分要求')),
                ('practical_credits_required', models.PositiveIntegerField(default=0, verbose_name='实践学分要求')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='courses.coursecategory', verbose_name='父分类')),
            ],
            options={
                'verbose_name': '课程分类',
                'verbose_name_plural': '课程分类',
                'db_table': 'course_categories',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.AddField(
            model_name='course',
            name='category',
            field=models.ForeignKey(blank=True, help_text='每门课程只能属于一个分类', null=True, on_delete=django.db.models.deletion.PROTECT, to='courses.coursecategory', verbose_name='课程分类'),
        ),
    ]
