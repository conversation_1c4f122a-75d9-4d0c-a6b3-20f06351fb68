import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import routes from './router/index'
import { useUserStore } from './stores/user'

const app = createApp(App)

const router = createRouter({
  history: createWebHistory(),
  routes
})

const pinia = createPinia()

// 添加路由守卫
router.beforeEach((to, from, next) => {
  // 先初始化Pinia，确保store可用
  if (!pinia._a) {
    app.use(pinia)
  }
  
  const userStore = useUserStore()
  const token = localStorage.getItem('token')
  
  // 如果用户已登录且访问登录页面，重定向到仪表盘
  if (token && (to.path === '/login' || to.path === '/register')) {
    next('/dashboard')
    return
  }
  
  // 如果页面需要认证但用户未登录，重定向到登录页面
  if (to.meta.requiresAuth && !token) {
    next('/login')
    return
  }
  
  next()
})

const pinia = createPinia()

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(ElementPlus)
app.use(pinia)
app.use(router)

// 初始化用户状态
const userStore = useUserStore()
userStore.initUserFromStorage()

app.mount('#app')
