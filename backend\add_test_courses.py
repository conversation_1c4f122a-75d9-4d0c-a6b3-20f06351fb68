#!/usr/bin/env python3
"""
为测试用户添加一些课程数据，以便测试更丰富的毕业预审结果
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'academic_journey.settings')
django.setup()

from django.contrib.auth import get_user_model
from courses.models import Course, UserCourse
from curriculum.models import CurriculumTemplate

User = get_user_model()

def add_test_courses():
    """为测试用户添加一些课程"""
    print("📚 为测试用户添加课程数据...")
    
    # 获取测试用户
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f"找到测试用户: {user.email}")
    except User.DoesNotExist:
        print("❌ 测试用户不存在")
        return False
    
    # 获取一些课程
    courses_to_add = [
        ('程序设计基础', '学科基础', 'completed', 85),
        ('数据结构与算法', '学科基础', 'completed', 88),
        ('计算机组成原理', '学科基础', 'completed', 82),
        ('操作系统', '专业必修', 'completed', 90),
        ('数据库系统原理', '专业必修', 'completed', 87),
        ('计算机网络', '专业必修', 'in_progress', None),
        ('人工智能导论', '专业选修', 'completed', 92),
        ('网络安全', '专业选修', 'completed', 85),
    ]
    
    added_count = 0
    for course_name, category, status, grade in courses_to_add:
        try:
            # 查找课程
            course = Course.objects.filter(course_name=course_name).first()
            if not course:
                print(f"⚠️ 课程不存在: {course_name}")
                continue
            
            # 检查用户是否已经有这门课程
            user_course, created = UserCourse.objects.get_or_create(
                user=user,
                course=course,
                defaults={
                    'status': status,
                    'grade': grade,
                    'semester_taken': '2023-2024-1'
                }
            )
            
            if created:
                print(f"✅ 添加课程: {course_name} ({status}) - {grade if grade else 'N/A'}")
                added_count += 1
            else:
                print(f"⚠️ 课程已存在: {course_name}")
                
        except Exception as e:
            print(f"❌ 添加课程失败 {course_name}: {e}")
    
    print(f"\n📊 总共添加了 {added_count} 门课程")
    
    # 显示用户当前的课程统计
    print("\n📈 用户课程统计:")
    user_courses = UserCourse.objects.filter(user=user)
    by_category = {}
    total_credits = 0
    
    for uc in user_courses:
        category = uc.course.category
        if category not in by_category:
            by_category[category] = {'count': 0, 'credits': 0}
        
        by_category[category]['count'] += 1
        by_category[category]['credits'] += uc.course.credits
        total_credits += uc.course.credits
    
    for category, stats in by_category.items():
        print(f"  {category}: {stats['count']}门课程, {stats['credits']}学分")
    
    print(f"  总计: {len(user_courses)}门课程, {total_credits}学分")
    
    return True

if __name__ == "__main__":
    print("🚀 开始为测试用户添加课程数据...")
    print("=" * 50)
    
    if add_test_courses():
        print("\n" + "=" * 50)
        print("✅ 课程数据添加完成!")
        print("💡 现在可以重新运行毕业预审测试，查看更丰富的结果")
    else:
        print("\n" + "=" * 50)
        print("❌ 课程数据添加失败!")
