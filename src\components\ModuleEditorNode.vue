<template>
  <div class="module-editor-node" :class="{'is-nested': !isRoot}">
    <div class="module-main">
      <div class="drag-handle">
        <el-icon><Rank /></el-icon>
      </div>
      <div class="module-inputs">
        <el-input 
          :model-value="module.name"
          @update:modelValue="$emit('update', { ...module, name: $event })"
          placeholder="模块名称 (例如：专业选修课)" 
        />
        <el-input-number 
          :model-value="module.credits_required"
          @update:modelValue="$emit('update', { ...module, credits_required: $event })"
          :min="0" 
          placeholder="模块要求总学分" 
        />
      </div>
      <div class="module-actions">
        <el-button type="primary" plain size="small" @click="$emit('add-child', module.id)">添加子模块</el-button>
        <el-button type="success" plain size="small" @click="$emit('add-rule', module.id)">添加规则</el-button>
        <el-button type="danger" circle plain size="small" @click="$emit('delete-module', module.id)">
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>
    </div>
    
    <!-- 规则编辑器 -->
    <div class="rules-editor" v-if="module.rules && module.rules.length > 0">
      <div v-for="(rule, index) in module.rules" :key="rule.id" class="rule-item">
        <el-input 
          :model-value="rule.name"
          @update:modelValue="updateRule(index, { ...rule, name: $event })"
          placeholder="规则名称" 
          class="rule-name-input" 
        />
        <el-input-number 
          :model-value="rule.credits_required"
          @update:modelValue="updateRule(index, { ...rule, credits_required: $event })"
          :min="0" 
          placeholder="学分要求" 
        />
        <el-select
          :model-value="rule.tags"
          @update:modelValue="updateRule(index, { ...rule, tags: $event })"
          multiple
          filterable
          allow-create
          default-first-option
          :reserve-keyword="false"
          placeholder="选择或创建所需课程标签"
          class="tag-selector"
        >
          <el-option
            v-for="tag in allTags"
            :key="tag.id"
            :label="tag.name"
            :value="tag.name"
          />
        </el-select>
        <el-button type="danger" circle plain size="small" @click="$emit('delete-rule', { moduleId: module.id, ruleIndex: index })">
          <el-icon><Delete /></el-icon>
        </el-button>
      </div>
    </div>

    <div class="module-children" v-if="module.children && module.children.length > 0">
      <ModuleEditorNode
        v-for="child in module.children"
        :key="child.id"
        :module="child"
        :all-tags="allTags"
        @update="$emit('update-child', $event)"
        @add-child="$emit('add-child', $event)"
        @add-rule="$emit('add-rule', $event)"
        @delete-module="$emit('delete-module', $event)"
        @delete-rule="$emit('delete-rule', $event)"
        :is-root="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import type { PropType } from 'vue';
import { Delete, Rank } from '@element-plus/icons-vue';
import type { CourseTag } from '../services/curriculum';

defineOptions({ name: 'ModuleEditorNode' });

interface Rule {
  id: string;
  name: string;
  credits_required: number;
  courses_required: number;
  tags: string[]; // Store tag names as strings
}

interface Module {
  id: string;
  name: string;
  credits_required: number;
  children: Module[];
  rules: Rule[];
}

const props = defineProps({
  module: {
    type: Object as PropType<Module>,
    required: true
  },
  allTags: {
    type: Array as PropType<CourseTag[]>,
    required: true
  },
  isRoot: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits([
  'update', 
  'update-child',
  'delete-module', 
  'add-child',
  'add-rule',
  'delete-rule'
]);

// Helper function to update a rule and emit the change
const updateRule = (ruleIndex: number, newRule: Rule) => {
  const updatedModule = { ...props.module };
  updatedModule.rules[ruleIndex] = newRule;
  emit('update', updatedModule);
}
</script>

<style scoped>
.module-editor-node {
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.is-nested {
  margin-left: 30px;
  border-left: 3px solid #cbd5e1;
}

.module-main {
  display: flex;
  align-items: center;
  gap: 12px;
}

.drag-handle {
  cursor: grab;
  color: #a0aec0;
}

.module-inputs {
  flex-grow: 1;
  display: flex;
  gap: 12px;
}

.module-actions {
  flex-shrink: 0;
}

.module-children {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px dashed #e2e8f0;
}

.rules-editor {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #e2e8f0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rule-item {
  display: flex;
  gap: 12px;
  align-items: center;
}

.rule-name-input {
  flex-basis: 30%;
}

.tag-selector {
  flex-grow: 1;
}
</style> 