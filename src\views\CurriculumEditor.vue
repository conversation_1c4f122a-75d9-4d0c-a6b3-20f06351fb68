<template>
  <div class="editor-page">
    <div class="editor-header">
      <h1>培养方案编辑器</h1>
      <p>在这里，你可以从零开始，构建完全个性化的培养方案。</p>
    </div>
    <div class="editor-content">
      <el-card>
        <template #header>
          <h2>基础信息</h2>
        </template>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="方案名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入培养方案名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="专业" prop="major">
                <el-input v-model="form.major" placeholder="请输入专业名称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="入学年份" prop="enrollment_year">
                <el-input v-model="form.enrollment_year" placeholder="请输入入学年份" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="总学分" prop="total_credits_required">
                <el-input-number v-model="form.total_credits_required" :min="1" :max="300" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="方案描述" prop="description">
            <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入培养方案描述（可选）" />
          </el-form-item>
        </el-form>
      </el-card>

      <el-card style="margin-top: 24px;">
        <template #header>
          <div class="card-header">
            <h2><el-icon><Grid /></el-icon> 毕业要求结构</h2>
            <el-button type="primary" @click="addRootModule">
              <el-icon><Plus /></el-icon> 添加主模块
            </el-button>
          </div>
        </template>
        <div class="module-tree">
           <el-empty v-if="form.modules.length === 0" description="暂无要求模块，请点击右上角添加" />
           <div v-else>
              <module-editor-node 
                v-for="module in form.modules"
                :key="module.id"
                :module="module"
                :all-tags="allTags"
                @update="handleUpdateModule"
                @delete-module="handleDeleteModule"
                @add-child="handleAddChildModule"
                @add-rule="handleAddRule"
                @delete-rule="handleDeleteRule"
                @update-child="handleUpdateModule"
              />
           </div>
        </div>
      </el-card>
       <div class="editor-actions">
        <el-button @click="goBack"><el-icon><ArrowLeft /></el-icon> 返回</el-button>
        <el-button type="primary" @click="savePlan">保存方案 <el-icon class="el-icon--right"><Check /></el-icon></el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { InfoFilled, Grid, ArrowLeft, Check, Plus } from '@element-plus/icons-vue';
import ModuleEditorNode from '../components/ModuleEditorNode.vue';
import { curriculumAPI, type CourseTag } from '../services/curriculum';

const router = useRouter();
const formRef = ref<FormInstance>();

// 为模块定义一个明确的接口
interface Module {
  id: string; // 使用字符串UUID以避免重复
  name: string;
  credits_required: number;
  children: Module[];
  rules: any[]; // 'any' for now
}

const form = reactive({
  name: '',
  major: '',
  enrollment_year: new Date().getFullYear().toString(),
  total_credits_required: 160,
  description: '',
  modules: [] as Module[] 
});

const allTags = ref<CourseTag[]>([]);

onMounted(async () => {
  try {
    allTags.value = await curriculumAPI.getCourseTags();
  } catch (error) {
    console.error("Failed to fetch course tags:", error);
    ElMessage.error("获取课程标签失败");
  }
});

const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入培养方案名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  major: [
    { required: true, message: '请输入专业名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  enrollment_year: [
    { required: true, message: '请输入入学年份', trigger: 'blur' }
  ],
  total_credits_required: [
    { required: true, message: '请输入总学分要求', trigger: 'blur' },
    { type: 'number', min: 1, max: 300, message: '学分数应在1-300之间', trigger: 'blur' }
  ]
});

// --- Recursive Helper ---
const findModuleById = (modules: Module[], id: string): { parent: Module[] | null, module: Module | null, index: number } => {
  for (let i = 0; i < modules.length; i++) {
    if (modules[i].id === id) {
      return { parent: modules, module: modules[i], index: i };
    }
    if (modules[i].children) {
      const found = findModuleById(modules[i].children, id);
      if (found.module) {
        return found;
      }
    }
  }
  return { parent: null, module: null, index: -1 };
};

// --- Event Handlers ---
const handleUpdateModule = (updatedModule: Module) => {
  const { parent, module, index } = findModuleById(form.modules, updatedModule.id);
  if (parent && module) {
    parent[index] = updatedModule;
  }
};

const handleDeleteModule = (moduleId: string) => {
  const { parent, index } = findModuleById(form.modules, moduleId);
  if (parent && index > -1) {
    parent.splice(index, 1);
  }
};

const handleAddChildModule = (parentId: string) => {
  const { module } = findModuleById(form.modules, parentId);
  if (module) {
    module.children.push({
      id: `module_${Date.now()}_${Math.random()}`,
      name: '新子模块',
      credits_required: 0,
      children: [],
      rules: []
    });
  }
};

const handleAddRule = (moduleId: string) => {
  const { module } = findModuleById(form.modules, moduleId);
  if (module) {
    module.rules.push({
      id: `rule_${Date.now()}_${Math.random()}`,
      name: '新规则',
      credits_required: 0,
      courses_required: 0,
      tags: []
    });
  }
};

const handleDeleteRule = ({ moduleId, ruleIndex }: { moduleId: string, ruleIndex: number }) => {
  const { module } = findModuleById(form.modules, moduleId);
  if (module && module.rules[ruleIndex]) {
    module.rules.splice(ruleIndex, 1);
  }
};

const addRootModule = () => {
  form.modules.push({
    id: `module_${Date.now()}_${Math.random()}`,
    name: '',
    credits_required: 0,
    children: [],
    rules: []
  });
};

const goBack = () => {
  router.back();
};

const savePlan = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      if (form.modules.length === 0) {
        ElMessage.warning('请至少添加一个毕业要求模块。');
        return;
      }
      try {
        ElMessage.info('正在保存您的方案...');
        const newTemplate = await curriculumAPI.createTemplateFromJson(form);
        ElMessage.success(`方案 "${newTemplate.major}" 已成功创建并激活！`);
        router.push('/curriculum');
      } catch (error) {
        console.error('创建培养方案失败:', error);
        ElMessage.error('保存失败，请检查网络或联系管理员。');
      }
    } else {
      ElMessage.error('表单信息不完整，请检查。');
      return false;
    }
  });
};

</script>

<style scoped>
.editor-page {
  padding: 24px;
  background-color: #f8fafc;
  height: 100%;
}
.editor-header {
  margin-bottom: 24px;
  text-align: center;
}
.editor-header h1 {
  font-size: 28px;
  font-weight: 700;
}
.editor-header p {
  color: #64748b;
}
.editor-content {
  max-width: 1000px;
  margin: 0 auto;
}
.editor-actions {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.module-tree {
  margin-top: 16px;
}
</style> 