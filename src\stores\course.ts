import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { courseAPI, type Course, type UserCourse, type NewUserCourseData, type UpdateUserCourseData } from '../services/course'
import { ElMessage } from 'element-plus'

export const useCourseStore = defineStore('course', () => {
  // 状态
  const courses = ref<Course[]>([])
  const userCourses = ref<UserCourse[]>([])
  const isLoading = ref(false)

  // 计算属性
  const coursesCount = computed(() => courses.value.length)
  const userCoursesCount = computed(() => userCourses.value.length)
  
  const coursesByStatus = computed(() => {
    return {
      planned: userCourses.value.filter(c => c.status === 'planned'),
      in_progress: userCourses.value.filter(c => c.status === 'in-progress'),
      completed: userCourses.value.filter(c => c.status === 'completed')
    }
  })

  const totalCredits = computed(() => {
    return userCourses.value
      .filter(c => c.status === 'completed')
      .reduce((sum, c) => sum + c.course.total_credits, 0)
  })

  const totalTheoreticalCredits = computed(() => {
    return userCourses.value
      .filter(c => c.status === 'completed')
      .reduce((sum, c) => sum + c.course.theoretical_credits, 0)
  })

  const totalPracticalCredits = computed(() => {
    return userCourses.value
      .filter(c => c.status === 'completed')
      .reduce((sum, c) => sum + c.course.practical_credits, 0)
  })

  // 简化的GPA计算：将字母成绩转换为数字
  const convertGradeToGPA = (grade: string | null): number | null => {
    if (!grade) return null
    
    const gradeMap: { [key: string]: number } = {
      'A+': 4.0, 'A': 4.0, 'A-': 3.7,
      'B+': 3.3, 'B': 3.0, 'B-': 2.7,
      'C+': 2.3, 'C': 2.0, 'C-': 1.7,
      'D+': 1.3, 'D': 1.0, 'F': 0.0
    }
    
    // 如果是数字成绩，转换为4.0制
    if (!isNaN(Number(grade))) {
      const numGrade = Number(grade)
      if (numGrade >= 90) return 4.0
      if (numGrade >= 80) return 3.0
      if (numGrade >= 70) return 2.0
      if (numGrade >= 60) return 1.0
      return 0.0
    }
    
    return gradeMap[grade.toUpperCase()] || null
  }

  const gpa = computed(() => {
    const completedCourses = userCourses.value.filter(
      c => c.status === 'completed' && c.grade
    )
    
    if (completedCourses.length === 0) return 0
    
    let totalPoints = 0
    let totalCredits = 0
    
    completedCourses.forEach(c => {
      const gradePoint = convertGradeToGPA(c.grade)
      if (gradePoint !== null) {
        totalPoints += gradePoint * c.course.total_credits
        totalCredits += c.course.total_credits
      }
    })
    
    return totalCredits > 0 ? totalPoints / totalCredits : 0
  })

  // 按学期分组的课程
  const coursesBySemester = computed(() => {
    const grouped: { [key: string]: UserCourse[] } = {}
    userCourses.value.forEach(course => {
      const semester = course.semester_taken
      if (!grouped[semester]) {
        grouped[semester] = []
      }
      grouped[semester].push(course)
    })
    return grouped
  })

  // 操作
  const fetchCourses = async () => {
    isLoading.value = true
    try {
      courses.value = await courseAPI.getAllCourses()
    } catch (error: any) {
      console.error('获取课程列表失败:', error)
      ElMessage.error('获取课程列表失败')
    } finally {
      isLoading.value = false
    }
  }

  const fetchUserCourses = async () => {
    isLoading.value = true
    try {
      userCourses.value = await courseAPI.getUserCourses()
    } catch (error: any) {
      console.error('获取用户课程失败:', error)
      ElMessage.error('获取用户课程失败')
    } finally {
      isLoading.value = false
    }
  }

  const addUserCourse = async (courseData: NewUserCourseData) => {
    try {
      const newCourse = await courseAPI.addUserCourse(courseData)
      userCourses.value.push(newCourse)
      ElMessage.success('课程添加成功')
      return newCourse
    } catch (error: any) {
      console.error('添加课程失败:', error)
      ElMessage.error(error.response?.data?.detail || '添加课程失败')
      throw error
    }
  }

  const updateUserCourse = async (id: number, courseData: UpdateUserCourseData) => {
    try {
      const updatedCourse = await courseAPI.updateUserCourse(id, courseData)
      const index = userCourses.value.findIndex(c => c.id === id)
      if (index !== -1) {
        userCourses.value[index] = updatedCourse
      }
      ElMessage.success('课程更新成功')
      return updatedCourse
    } catch (error: any) {
      console.error('更新课程失败:', error)
      ElMessage.error(error.response?.data?.detail || '更新课程失败')
      throw error
    }
  }

  const deleteUserCourse = async (id: number) => {
    try {
      await courseAPI.deleteUserCourse(id)
      const index = userCourses.value.findIndex(c => c.id === id)
      if (index !== -1) {
        userCourses.value.splice(index, 1)
      }
      ElMessage.success('课程删除成功')
    } catch (error: any) {
      console.error('删除课程失败:', error)
      ElMessage.error(error.response?.data?.detail || '删除课程失败')
      throw error
    }
  }

  const getCourseById = (id: number) => {
    return courses.value.find(c => c.id === id)
  }

  const getUserCourseById = (id: number) => {
    return userCourses.value.find(c => c.id === id)
  }

  return {
    // 状态
    courses,
    userCourses,
    isLoading,
    
    // 计算属性
    coursesCount,
    userCoursesCount,
    coursesByStatus,
    coursesBySemester,
    totalCredits,
    totalTheoreticalCredits,
    totalPracticalCredits,
    gpa,
    
    // 操作
    fetchCourses,
    fetchUserCourses,
    addUserCourse,
    updateUserCourse,
    deleteUserCourse,
    getCourseById,
    getUserCourseById
  }
})

export default useCourseStore
