# Copilot Instructions for Academic Journey Steward

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Overview

This project is "学途管家" (Academic Journey Steward), a personalized academic planning and progress tracking platform for university students.

## Tech Stack

- **Frontend**: Vue 3 + TypeScript + Vite + Element Plus + Pinia + Vue Router + ECharts
- **Backend**: Django + Django REST Framework + PostgreSQL
- **Deployment**: Docker containerization

## Core Modules

1. **User Center Module**: User registration, login, profile management
2. **Curriculum Management Module**: Academic plan templates, course management
3. **My Journey Module**: Course timeline, status tracking, grade input
4. **Dashboard Module**: Progress visualization, GPA calculator
5. **Graduation Audit Module**: Graduation requirement checking

## Database Schema

- Users: user_id, username, email, school, major, enrollment_year
- Courses: course_id, course_code, course_name, credits
- Curriculum_Templates: template_id, school, major, total_credits_required
- User_Courses: user_id, course_id, status, grade, semester_taken

## Key Features

- Drag-and-drop course planning
- Visual progress tracking with charts
- Automated graduation requirement auditing
- GPA calculation
- Semester-based course organization

## Code Style

- Use TypeScript for frontend development
- Follow Vue 3 Composition API patterns
- Use Pinia for state management
- Follow Django best practices for backend
- Use meaningful variable and function names in Chinese context
- Add proper type annotations and documentation
