# 学途管家 (Academic Journey Steward)

一个面向大学生的个性化学业规划与进度追踪平台。

## 项目愿景

打造一个面向大学生的、个性化的一站式学业规划与进度追踪平台，帮助学生：

- 消除信息差：将厚重的、格式不一的培养方案数字化、结构化
- 减轻学业焦虑：让学生随时清晰地了解"我修了多少"、"还差多少"、"毕业要求是否满足"
- 赋能自主规划：提供工具，帮助学生规划选修课、辅修专业，甚至提前毕业
- 提升学习效率：通过清晰的计划，让学生专注于当前学期的任务

## 技术栈

### 前端
- Vue 3 + TypeScript
- Vite (构建工具)
- Element Plus (UI组件库)
- Pinia (状态管理)
- Vue Router (路由)
- ECharts (图表可视化)
- Axios (HTTP客户端)

### 后端
- Python 3.12
- Django 5.2.3
- Django REST Framework
- PostgreSQL (数据库)
- Django CORS Headers

## 核心功能模块

### 1. 用户中心模块
- 用户注册/登录
- 个人信息管理
- 账户安全设置

### 2. 培养方案管理模块
- 官方方案库
- 方案导入/创建
- 个性化调整

### 3. 我的学途模块 (核心)
- 课程总览时间轴
- 课程状态管理
- 成绩录入
- 拖拽规划

### 4. 进度仪表盘模块
- 可视化图表展示
- GPA计算器
- 当前学期速览

### 5. 毕业预审模块
- 一键审查
- 生成审查报告
- 智能建议

## 快速开始

### 前端开发

```bash
# 安装依赖
npm install

# 启动开发服务器  
npm run dev

# 构建生产版本
npm run build
```

### 后端开发

```bash
# 进入后端目录
cd backend

# 激活虚拟环境
.\venv\Scripts\Activate.ps1  # Windows
source venv/bin/activate     # macOS/Linux

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py migrate

# 启动开发服务器
python manage.py runserver
```

## 联系方式

如果您有任何问题或建议，请通过 Issues 与我们联系。

---

让我们一起打造更好的学业规划工具！ 🎓✨
