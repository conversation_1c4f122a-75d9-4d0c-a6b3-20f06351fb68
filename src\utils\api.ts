import axios from 'axios'
import { useUserStore } from '../stores/user'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://127.0.0.1:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Token ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    // Check if the error is a 401 Unauthorized
    if (error.response?.status === 401) {
      // We need to get a fresh instance of the store here inside the interceptor
      const userStore = useUserStore()
      // Only trigger logout if there was a token to begin with,
      // to avoid loops on public pages that might 401.
      if (userStore.token) {
        userStore.logout()
      }
      // Return a rejected promise to stop the promise chain
      return Promise.reject(error)
    }
    
    // For all other errors, just forward them
    return Promise.reject(error)
  }
)

export default api
