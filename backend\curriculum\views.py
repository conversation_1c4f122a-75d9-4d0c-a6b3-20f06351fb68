from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Sum, Q
from django.utils import timezone
from django.db.models.deletion import ProtectedError
from django.db.transaction import atomic
from django.db import transaction
# from .models import CurriculumTemplate, TemplateCourse, CreditRequirement, UserCreditRequirement
from .models import CurriculumTemplate, TemplateCourse, UserCreditRequirement, RequirementModule, Rule, UserCurriculumTemplate, CourseTag
from courses.models import UserCourse
from .serializers import (
    CurriculumTemplateSerializer, 
    TemplateCourseSerializer, 
    # CreditRequirementSerializer,
    UserCreditRequirementSerializer,
    RequirementModuleSerializer,
    CourseTagSerializer
)


class CurriculumTemplateViewSet(viewsets.ModelViewSet):
    queryset = CurriculumTemplate.objects.all()
    serializer_class = CurriculumTemplateSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """根据用户的学校过滤培养方案，包括用户自定义的方案"""
        user = self.request.user
        queryset = CurriculumTemplate.objects.all()
        
        # 显示匹配用户学校的方案，以及用户自定义的方案
        if user.school:
            # 用户的学校方案 或者 用户自定义方案（包含用户名的）
            queryset = queryset.filter(
                Q(school=user.school) | 
                Q(major__contains=f"(用户: {user.username})")
            )
        else:
            # 如果用户没有学校信息，只显示自定义方案
            queryset = queryset.filter(major__contains=f"(用户: {user.username})")
        
        return queryset
    
    @action(detail=True, methods=['get'])
    def template_courses(self, request, pk=None):
        """获取培养方案的课程"""
        template = self.get_object()
        courses = TemplateCourse.objects.filter(template=template)
        serializer = TemplateCourseSerializer(courses, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def modules(self, request, pk=None):
        """获取培养方案的所有要求模块"""
        template = self.get_object()
        modules = RequirementModule.objects.filter(template=template, parent__isnull=True) # 只获取顶层模块
        serializer = RequirementModuleSerializer(modules, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], url_path='activate')
    def activate(self, request, pk=None):
        """将当前用户与此培养方案模板关联并设为激活状态"""
        template = self.get_object()
        user = request.user

        with atomic():
            # 将该用户其他所有方案设为非激活
            UserCurriculumTemplate.objects.filter(user=user).update(is_active=False)
            
            # 获取或创建新的关联，并设为激活
            user_template, created = UserCurriculumTemplate.objects.get_or_create(
                user=user,
                template=template,
                defaults={'is_active': True}
            )
            
            if not created:
                user_template.is_active = True
                user_template.save()

        return Response(
            {'status': 'success', 'message': f'培养方案 "{template.major}" 已激活'},
            status=status.HTTP_200_OK
        )

    def _create_modules_recursive(self, modules_data, template, parent_module=None):
        """递归创建模块和子模块，现在也包括规则和标签"""
        for module_data in modules_data:
            children_data = module_data.pop('children', [])
            rules_data = module_data.pop('rules', [])
            
            new_module = RequirementModule.objects.create(
                template=template,
                parent=parent_module,
                name=module_data.get('name', '未命名模块'),
                credits_required=module_data.get('credits_required', 0),
                description=module_data.get('description', '')
            )
            
            # 创建规则和标签
            for rule_data in rules_data:
                # 首先 get_or_create 所有的标签
                tags_for_rule = []
                tag_names = rule_data.get('tags', [])
                for tag_name in tag_names:
                    tag, _ = CourseTag.objects.get_or_create(name=tag_name)
                    tags_for_rule.append(tag)
                
                # 创建规则并关联标签
                new_rule = Rule.objects.create(
                    module=new_module,
                    name=rule_data.get('name', '未命名规则'),
                    credits_required=rule_data.get('credits_required', 0),
                    courses_required=rule_data.get('courses_required', 0)
                )
                if tags_for_rule:
                    new_rule.tags.set(tags_for_rule)

            # 如果有子模块，递归创建
            if children_data:
                self._create_modules_recursive(children_data, template, new_module)
    
    @action(detail=False, methods=['post'], url_path='create-from-json')
    @transaction.atomic
    def create_from_json(self, request):
        """从一个JSON对象创建完整的培养方案模板和其模块结构"""
        data = request.data
        user = request.user

        plan_name = data.get('name')
        if not plan_name:
            return Response({'error': '方案名称(name)是必填项'}, status=status.HTTP_400_BAD_REQUEST)

        # 使用方案名称作为独一无二的'major'字段，并将真实专业放入描述
        academic_major = data.get('major', '未指定专业')
        description = f"专业: {academic_major}. {data.get('description', '')}"

        # 为保证用户自定义方案的唯一性，附加用户信息
        unique_plan_name = f"{plan_name} (用户: {user.username})"

        template_data = {
            'school': user.school or '自定义',
            'major': unique_plan_name, # 使用处理过的唯一名称
            'enrollment_year': data.get('enrollment_year'),
            'total_credits_required': data.get('total_credits_required', 0),
            'description': description
        }

        # 使用 unique_plan_name 进行查找或创建
        template, created = CurriculumTemplate.objects.get_or_create(
            major=unique_plan_name,
            school=user.school or '自定义',
            enrollment_year=data.get('enrollment_year'),
            defaults=template_data
        )
        
        if not created:
            # 如果已存在，则更新它
            template.description = description
            template.total_credits_required = data.get('total_credits_required', 0)
            template.save()
            template.modules.all().delete() # 清理旧模块，准备重建
        
        # 递归创建模块
        modules_data = data.get('modules', [])
        self._create_modules_recursive(modules_data, template)

        # 自动将新创建的方案设为该用户的激活方案
        UserCurriculumTemplate.objects.filter(user=user).update(is_active=False)
        UserCurriculumTemplate.objects.update_or_create(
            user=user,
            template=template,
            defaults={'is_active': True}
        )

        serializer = CurriculumTemplateSerializer(template)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class TemplateCourseViewSet(viewsets.ModelViewSet):
    queryset = TemplateCourse.objects.all()
    serializer_class = TemplateCourseSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """根据模板ID过滤课程"""
        queryset = TemplateCourse.objects.all()
        template_id = self.request.query_params.get('template_id')
        if template_id:
            queryset = queryset.filter(template_id=template_id)
        return queryset


class UserCreditRequirementViewSet(viewsets.ModelViewSet):
    """用户自定义学分要求的增删改查"""
    serializer_class = UserCreditRequirementSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """用户只能看到自己的自定义要求"""
        return UserCreditRequirement.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        """创建时自动关联用户"""
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['post'], url_path='batch-update')
    def batch_update(self, request, *args, **kwargs):
        """
        批量更新或创建用户的自定义学分要求.
        这个操作是幂等的：先删除该用户该模板下的所有自定义要求, 然后重新创建.
        """
        template_id = request.data.get('template_id')
        requirements_data = request.data.get('requirements')

        if not template_id or not isinstance(requirements_data, list):
            return Response({'error': '缺少 template_id 或 requirements 数据'}, status=400)

        user = request.user

        # 验证模板是否存在
        try:
            template = CurriculumTemplate.objects.get(id=template_id)
        except CurriculumTemplate.DoesNotExist:
            return Response({'error': '指定的培养方案模板不存在'}, status=404)

        # 删除旧的自定义要求
        UserCreditRequirement.objects.filter(user=user, template=template).delete()

        # 准备创建新的自定义要求
        new_requirements_data = []
        for req_data in requirements_data:
            req_data['template'] = template.id
            req_data['user'] = user.id  # 直接关联用户ID
            new_requirements_data.append(req_data)

        # 批量创建
        serializer = self.get_serializer(data=new_requirements_data, many=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()  # 不再调用 perform_create
        
        return Response(serializer.data, status=201)


class GraduationAuditViewSet(viewsets.ViewSet):
    """毕业预审API"""
    permission_classes = [IsAuthenticated]

    def _get_user_completed_courses(self, user):
        """获取用户所有已完成的课程, 缓存结果以提高效率"""
        if hasattr(self, '_completed_courses'):
            return self._completed_courses
        
        completed_courses = UserCourse.objects.filter(
            user=user,
            status='completed'
        ).select_related('course').prefetch_related('course__tags')
        
        self._completed_courses = list(completed_courses)
        return self._completed_courses

    def _evaluate_rule(self, rule, user_courses, used_courses):
        """评估单条规则，并返回满足条件的课程（避免重复使用）"""
        rule_tags_ids = set(rule.tags.values_list('id', flat=True))
        
        met_courses_set = set()
        
        # 这条规则没有设置任何标签作为课程池，直接认为满足（因为它不要求任何课程）
        if not rule_tags_ids:
            return {
                "id": rule.id, "name": rule.name, "description": rule.description,
                "is_met": True, "credits_required": 0, "credits_completed": 0,
                "courses_required": 0, "courses_completed": 0,
                "met_courses_set": set(), "tags": [], "tag_match_logic": rule.tag_match_logic
            }

        available_courses = [c for c in user_courses if c.id not in used_courses]

        for user_course in available_courses:
            course_tags_ids = {tag.id for tag in user_course.course.tags.all()}
            
            is_match = False
            if rule.tag_match_logic == 'ALL':
                if rule_tags_ids.issubset(course_tags_ids):
                    is_match = True
            else: # ANY
                if not rule_tags_ids.isdisjoint(course_tags_ids):
                    is_match = True
            
            if is_match:
                met_courses_set.add(user_course)

        completed_credits = sum(uc.course.credits for uc in met_courses_set)
        completed_courses_count = len(met_courses_set)

        credits_met = completed_credits >= rule.credits_required
        courses_met = completed_courses_count >= rule.courses_required
        is_rule_met = credits_met and courses_met
        
        serialized_tags = CourseTagSerializer(rule.tags.all(), many=True).data

        return {
            "id": rule.id,
            "name": rule.name,
            "description": rule.description,
            "is_met": is_rule_met,
            "credits_required": rule.credits_required,
            "credits_completed": completed_credits,
            "courses_required": rule.courses_required,
            "courses_completed": completed_courses_count,
            "met_courses_set": met_courses_set,
            "tags": serialized_tags,
            "tag_match_logic": rule.tag_match_logic,
        }

    def _evaluate_module(self, module, user_courses, used_course_ids):
        """递归评估一个模块及其所有子模块"""
        
        module_met_courses = set()
        rules_results = []
        
        # 评估当前模块的所有规则
        for rule in module.rules.all():
            rule_result = self._evaluate_rule(rule, user_courses, used_course_ids)
            rules_results.append(rule_result)
            # 将满足此规则的课程加入模块课程池
            module_met_courses.update(rule_result['met_courses_set'])

        # 递归评估子模块
        children_results = []
        for child in module.children.all():
            child_result = self._evaluate_module(child, user_courses, used_course_ids)
            children_results.append(child_result)
            # 将满足子模块的课程也加入本模块的课程池
            module_met_courses.update(child_result['all_met_courses_in_module_set'])

        # 计算模块总目标
        completed_credits = sum(course.course.credits for course in module_met_courses)
        completed_courses_count = len(module_met_courses)
        
        module_credits_met = completed_credits >= module.credits_required
        module_courses_met = completed_courses_count >= module.courses_required
        is_module_target_met = module_credits_met and module_courses_met

        # 模块的最终状态取决于其自身目标、所有规则、所有子模块的状态
        all_rules_met = all(res['is_met'] for res in rules_results)
        all_children_met = all(res['is_met'] for res in children_results)
        is_module_fully_met = is_module_target_met and all_rules_met and all_children_met

        return {
            "id": module.id,
            "name": module.name,
            "description": module.description,
            "is_met": is_module_fully_met,
            "credits_required": module.credits_required,
            "credits_completed": completed_credits,
            "courses_required": module.courses_required,
            "courses_completed": completed_courses_count,
            "rules": rules_results,
            "children": children_results,
            "met_courses": [{
                'courseCode': c.course.course_code,
                'courseName': c.course.course_name,
                'credits': c.course.credits,
            } for c in module_met_courses],
            "all_met_courses_in_module_set": module_met_courses
        }

    @action(detail=False, methods=['get'])
    def audit(self, request):
        """执行基于规则引擎的毕业预审"""
        user = request.user
        
        try:
            user_template = UserCurriculumTemplate.objects.get(user=user, is_active=True)
            template = user_template.template
        except UserCurriculumTemplate.DoesNotExist:
            return Response({'error': '未找到为该用户激活的培养方案模板'}, status=404)
        except UserCurriculumTemplate.MultipleObjectsReturned:
             return Response({'error': '用户有多个激活的培养方案，请联系管理员'}, status=400)

        user_completed_courses = self._get_user_completed_courses(user)
        
        top_level_modules = RequirementModule.objects.filter(
            template=template, 
            parent__isnull=True
        ).prefetch_related('rules__tags', 'children')
        
        # 在顶层进行评估，课程可以在不同顶层模块间复用
        audit_details = [self._evaluate_module(module, user_completed_courses, set()) for module in top_level_modules]
        
        total_modules = len(audit_details)
        met_modules = sum(1 for detail in audit_details if detail['is_met'])
        overall_status = 'passed' if total_modules > 0 and total_modules == met_modules else 'failed'
        
        summary = f"共 {total_modules} 项毕业要求，已满足 {met_modules} 项。"
        if overall_status == 'passed':
            summary = "恭喜！您已满足所有毕业要求。"

        return Response({
            'auditResult': {
                'overallStatus': overall_status,
                'summary': summary,
                'totalRequirements': total_modules,
                'metRequirements': met_modules,
                'unmetRequirements': total_modules - met_modules,
                'auditDate': timezone.now().strftime('%Y-%m-%d')
            },
            'auditDetails': audit_details,
            'template': {
                'id': template.id,
                'school': template.school,
                'major': template.major,
                'totalCreditsRequired': template.total_credits_required
            }
        })

class CourseTagViewSet(viewsets.ReadOnlyModelViewSet):
    """提供所有课程标签的只读列表"""
    queryset = CourseTag.objects.all().order_by('name')
    serializer_class = CourseTagSerializer
    permission_classes = [IsAuthenticated]
