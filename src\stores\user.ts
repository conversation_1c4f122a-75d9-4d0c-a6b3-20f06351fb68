import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI, type User, type LoginData } from '../services/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userInfo = computed(() => user.value)

  // 操作
  const login = async (loginData: LoginData) => {
    isLoading.value = true
    try {
      const response = await authAPI.login(loginData)
      
      // 保存token和用户信息
      token.value = response.token
      user.value = response.user
      
      // 持久化存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      ElMessage.success('登录成功')
      return response
    } catch (error: any) {
      console.error('登录失败:', error)
      ElMessage.error(error.response?.data?.error || '登录失败，请检查邮箱和密码')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    // 清除状态
    user.value = null
    token.value = null
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    
    // 跳转到登录页，并重新加载页面以确保所有状态都被重置
    window.location.href = '/login'
  }

  const initUserFromStorage = () => {
    const storedToken = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user')
    
    if (storedToken && storedUser) {
      token.value = storedToken
      try {
        user.value = JSON.parse(storedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  const updateProfile = async (profileData: Partial<User>) => {
    isLoading.value = true
    try {
      const updatedUser = await authAPI.updateProfile(profileData)
      user.value = updatedUser
      
      // 更新本地存储
      localStorage.setItem('user', JSON.stringify(updatedUser))
      
      ElMessage.success('个人信息更新成功')
      return updatedUser
    } catch (error: any) {
      console.error('更新个人信息失败:', error)
      ElMessage.error(error.response?.data?.error || '更新失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }
  const refreshProfile = async () => {
    if (!token.value) return
    
    try {
      const profile = await authAPI.getProfile()
      user.value = profile
      localStorage.setItem('user', JSON.stringify(profile))
    } catch (error: any) {
      console.error('刷新用户信息失败:', error)
      // 如果token无效，自动登出
      if (error.response?.status === 401) {
        logout()
      }
    }
  }

  return {
    // 状态
    user: userInfo,
    token,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    
    // 操作
    login,
    logout,
    initUserFromStorage,
    updateProfile,
    refreshProfile
  }
})

export default useUserStore
