from rest_framework import serializers
from .models import CurriculumTemplate, TemplateCourse, UserCreditRequirement, RequirementModule, Rule
from courses.models import CourseTag, CourseCategory
from courses.serializers import CourseSerializer, CourseCategorySerializer


class CurriculumTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CurriculumTemplate
        fields = '__all__'


class TemplateCourseSerializer(serializers.ModelSerializer):
    course = CourseSerializer(read_only=True)
    
    class Meta:
        model = TemplateCourse
        fields = ['id', 'course', 'recommended_semester', 'is_required']


# class CreditRequirementSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = CreditRequirement
#         fields = '__all__'


class UserCreditRequirementSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserCreditRequirement
        fields = '__all__'
        read_only_fields = ('user',)


class CourseTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = CourseTag
        fields = '__all__'


class RuleSerializer(serializers.ModelSerializer):
    tags = CourseTagSerializer(many=True, read_only=True)
    course_categories = serializers.SerializerMethodField()

    class Meta:
        model = Rule
        fields = ['id', 'module', 'name', 'description', 'total_credits_required',
                 'practical_credits_required', 'courses_required', 'course_categories',
                 'tags', 'category_match_logic', 'tag_match_logic']

    def get_course_categories(self, obj):
        from courses.serializers import CourseCategorySerializer
        return CourseCategorySerializer(obj.course_categories.all(), many=True).data


class RequirementModuleSerializer(serializers.ModelSerializer):
    rules = RuleSerializer(many=True, read_only=True)
    children = serializers.SerializerMethodField()
    course_category = serializers.SerializerMethodField()

    class Meta:
        model = RequirementModule
        fields = [
            'id', 'name', 'description', 'total_credits_required',
            'practical_credits_required', 'courses_required', 'parent',
            'course_category', 'order', 'rules', 'children'
        ]

    def get_children(self, obj):
        """递归地序列化子模块"""
        children = RequirementModule.objects.filter(parent=obj)
        if children.exists():
            return RequirementModuleSerializer(children, many=True).data
        return None

    def get_course_category(self, obj):
        if obj.course_category:
            from courses.serializers import CourseCategorySerializer
            return CourseCategorySerializer(obj.course_category).data
        return None
