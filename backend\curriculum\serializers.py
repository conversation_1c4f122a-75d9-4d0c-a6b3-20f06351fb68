from rest_framework import serializers
# from .models import CurriculumTemplate, TemplateCourse, CreditRequirement, UserCreditRequirement
from .models import CurriculumTemplate, TemplateCourse, UserCreditRequirement, RequirementModule, Rule, CourseTag
from courses.serializers import CourseSerializer


class CurriculumTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CurriculumTemplate
        fields = '__all__'


class TemplateCourseSerializer(serializers.ModelSerializer):
    course = CourseSerializer(read_only=True)
    
    class Meta:
        model = TemplateCourse
        fields = ['id', 'course', 'recommended_semester', 'is_required']


# class CreditRequirementSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = CreditRequirement
#         fields = '__all__'


class UserCreditRequirementSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserCreditRequirement
        fields = '__all__'
        read_only_fields = ('user',)


class CourseTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = CourseTag
        fields = '__all__'


class RuleSerializer(serializers.ModelSerializer):
    tags = CourseTagSerializer(many=True, read_only=True)
    class Meta:
        model = Rule
        fields = '__all__'


class RequirementModuleSerializer(serializers.ModelSerializer):
    rules = RuleSerializer(many=True, read_only=True)
    children = serializers.SerializerMethodField()

    class Meta:
        model = RequirementModule
        fields = [
            'id', 'name', 'description', 'credits_required', 
            'courses_required', 'parent', 'rules', 'children'
        ]

    def get_children(self, obj):
        """递归地序列化子模块"""
        children = RequirementModule.objects.filter(parent=obj)
        if children.exists():
            return RequirementModuleSerializer(children, many=True).data
        return None
