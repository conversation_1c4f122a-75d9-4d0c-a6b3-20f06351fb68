from django.urls import path
from .views import UserRegisterView, UserLoginView, UserProfileView, ChangePasswordView
from rest_framework.authtoken.views import obtain_auth_token

urlpatterns = [
    path('register/', UserRegisterView.as_view(), name='user-register'),
    path('login/', UserLoginView.as_view(), name='user-login'),
    path('profile/', UserProfileView.as_view(), name='user-profile'),
    path('change-password/', ChangePasswordView.as_view(), name='change-password'),
    path('token/', obtain_auth_token, name='api_token_auth'),
]
