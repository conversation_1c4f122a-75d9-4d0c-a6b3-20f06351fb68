# Generated by Django 5.2.3 on 2025-06-24 06:42

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('courses', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CurriculumTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('school', models.CharField(max_length=100, verbose_name='学校')),
                ('major', models.CharField(max_length=100, verbose_name='专业')),
                ('enrollment_year', models.CharField(max_length=10, verbose_name='适用年份')),
                ('total_credits_required', models.IntegerField(verbose_name='总学分要求')),
                ('description', models.TextField(blank=True, verbose_name='方案描述')),
                ('is_active', models.<PERSON><PERSON>anField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '培养方案模板',
                'verbose_name_plural': '培养方案模板',
                'db_table': 'curriculum_templates',
                'unique_together': {('school', 'major', 'enrollment_year')},
            },
        ),
        migrations.CreateModel(
            name='UserCurriculumTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否当前使用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='curriculum.curriculumtemplate', verbose_name='培养方案模板')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户培养方案',
                'verbose_name_plural': '用户培养方案',
                'db_table': 'user_curriculum_templates',
            },
        ),
        migrations.CreateModel(
            name='CreditRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(max_length=50, verbose_name='课程类别')),
                ('required_credits', models.IntegerField(verbose_name='要求学分')),
                ('description', models.TextField(blank=True, verbose_name='要求描述')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='curriculum.curriculumtemplate', verbose_name='培养方案模板')),
            ],
            options={
                'verbose_name': '学分要求',
                'verbose_name_plural': '学分要求',
                'db_table': 'credit_requirements',
                'unique_together': {('template', 'category')},
            },
        ),
        migrations.CreateModel(
            name='TemplateCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(max_length=50, verbose_name='课程类别')),
                ('recommended_semester', models.IntegerField(verbose_name='推荐学期')),
                ('is_required', models.BooleanField(default=True, verbose_name='是否必修')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='courses.course', verbose_name='课程')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='curriculum.curriculumtemplate', verbose_name='培养方案模板')),
            ],
            options={
                'verbose_name': '模板课程',
                'verbose_name_plural': '模板课程',
                'db_table': 'template_courses',
                'unique_together': {('template', 'course')},
            },
        ),
    ]
