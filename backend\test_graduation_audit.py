#!/usr/bin/env python3
"""
测试毕业预审API
"""

import requests
import json

# API配置
BASE_URL = 'http://localhost:8000'
API_URL = f'{BASE_URL}/api'

def test_graduation_audit():
    """测试毕业预审API"""
    print("🧪 测试毕业预审API...")
    
    # 1. 首先登录获取token
    login_data = {
        'email': '<EMAIL>',
        'password': 'password123'
    }
    
    print("1. 登录获取token...")
    try:
        response = requests.post(f'{API_URL}/users/login/', json=login_data)
        if response.status_code == 200:
            token = response.json()['token']
            print(f"✅ 登录成功，token: {token[:20]}...")
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    # 2. 测试毕业预审API
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    print("2. 调用毕业预审API...")
    try:
        response = requests.get(f'{API_URL}/curriculum/graduation-audit/audit/', headers=headers)
        
        if response.status_code == 200:
            audit_data = response.json()
            print("✅ 毕业预审API调用成功!")
            
            # 打印审查结果摘要
            audit_result = audit_data.get('auditResult', {})
            print(f"\n📊 预审结果摘要:")
            print(f"  整体状态: {'✅ 通过' if audit_result.get('overallStatus') == 'passed' else '⚠️ 未通过'}")
            print(f"  总要求项: {audit_result.get('totalRequirements', 0)}")
            print(f"  已满足: {audit_result.get('metRequirements', 0)}")
            print(f"  未满足: {audit_result.get('unmetRequirements', 0)}")
            print(f"  完成率: {round((audit_result.get('metRequirements', 0) / max(audit_result.get('totalRequirements', 1), 1)) * 100)}%")
            print(f"  摘要: {audit_result.get('summary', '无')}")
            
            # 打印学分类别详情
            audit_details = audit_data.get('auditDetails', [])
            print(f"\n📚 学分类别详情:")
            for category in audit_details:
                status_icon = "✅" if category.get('status') == 'passed' else "⚠️"
                print(f"  {status_icon} {category.get('name')}: {category.get('completedCredits', 0)}/{category.get('requiredCredits', 0)} 学分")
            
            # 打印建议
            recommendations = audit_data.get('recommendations', [])
            if recommendations:
                print(f"\n💡 建议与提醒:")
                for rec in recommendations:
                    icon = {"info": "ℹ️", "warning": "⚠️", "success": "✅"}.get(rec.get('type'), "📝")
                    print(f"  {icon} {rec.get('title')}: {rec.get('content')}")
            
            return True
            
        else:
            print(f"❌ 毕业预审API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 毕业预审API请求失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试毕业预审功能...")
    print("=" * 60)
    
    success = test_graduation_audit()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 毕业预审功能测试通过!")
    else:
        print("❌ 毕业预审功能测试失败!")
