import api from '../utils/api'

// 学分统计相关的API接口类型定义
export interface CreditStats {
  category: string
  required_credits: number
  completed_credits: number
  percentage: number
}

export interface GraduationProgress {
  total_required: number
  total_completed: number
  total_percentage: number
  category_stats: CreditStats[]
  remaining_credits: number
  estimated_semesters: number
}

// 学分统计API
export const statsAPI = {
  // 获取学分统计
  getCreditStats: (): Promise<GraduationProgress> => {
    return api.get('/stats/credits/')
  },

  // 获取GPA趋势
  getGpaTrend: (): Promise<any> => {
    return api.get('/stats/gpa-trend/')
  },

  // 获取学期进度
  getSemesterProgress: (): Promise<any> => {
    return api.get('/stats/semester-progress/')
  }
}

export default statsAPI
