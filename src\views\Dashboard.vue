<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>进度仪表盘</h1>
      <p>欢迎回来，{{ userStore.user?.real_name || userStore.user?.username }}！让我们看看你的学业进展</p>
    </div>

    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-credits">
              <el-icon><Document /></el-icon>
            </div>            <div class="stat-info">
              <h3>{{ progressStats.totalCredits }}</h3>
              <p>总学分</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed-credits">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ progressStats.completedCredits }}</h3>
              <p>已修学分</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon gpa">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ progressStats.gpa }}</h3>
              <p>当前GPA</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon remaining-semesters">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ progressStats.remainingSemesters }}</h3>
              <p>剩余学期</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card title="学分进度">
          <template #header>
            <span>学分进度</span>
          </template>
          <div class="progress-container">
            <el-progress
              :percentage="progressPercentage"
              :stroke-width="20"
              :text-inside="true"
              status="success"
            />
            <p class="progress-text">
              已完成 {{ progressStats.completedCredits }} / {{ progressStats.totalCredits }} 学分
            </p>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>学分分布</span>
          </template>
          <div ref="pieChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="current-semester-row">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="semester-header">
              <span>当前学期课程</span>
              <el-tag type="primary">{{ currentSemester }}</el-tag>
            </div>
          </template>          <el-table :data="currentCourses" style="width: 100%">
            <el-table-column prop="course.course_code" label="课程代码" width="120" />
            <el-table-column prop="course.course_name" label="课程名称" />
            <el-table-column label="学分" width="120">
              <template #default="scope">
                <div class="credits-info">
                  <span class="total-credits">{{ scope.row.course.total_credits }}</span>
                  <span class="credits-breakdown">({{ scope.row.course.theoretical_credits }}+{{ scope.row.course.practical_credits }})</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag
                  :type="getStatusType(scope.row.status)"
                  size="small"
                >
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="grade" label="成绩" width="80">
              <template #default="scope">
                {{ scope.row.grade || '-' }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Document, Check, TrendCharts, Calendar } from '@element-plus/icons-vue'
import { useUserStore } from '../stores/user'
import { useCourseStore } from '../stores/course'
import * as echarts from 'echarts/core'
import { PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  PieChart,
  CanvasRenderer
])

const pieChartRef = ref<HTMLDivElement>()

// 使用stores
const userStore = useUserStore()
const courseStore = useCourseStore()

const progressStats = computed(() => ({
  totalCredits: 151, // 更新为新的总学分要求
  completedCredits: courseStore.totalCredits,
  gpa: Number(courseStore.gpa.toFixed(2)),
  remainingSemesters: Math.ceil((151 - courseStore.totalCredits) / 20) // 假设每学期修20学分
}))

const currentSemester = ref('2024-2025学年第1学期')

const currentCourses = computed(() => {
  // 获取当前学期的课程（2024-1学期）
  const semester = '2024-1'
  return courseStore.coursesBySemester[semester] || []
})

const progressPercentage = computed(() => {
  return Math.round((progressStats.value.completedCredits / progressStats.value.totalCredits) * 100)
})

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'planned': 'info',
    'in-progress': 'warning',
    'completed': 'success'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'planned': '计划中',
    'in-progress': '进行中',
    'completed': '已完成'
  }
  return texts[status] || '未知'
}

const initPieChart = () => {
  if (!pieChartRef.value) return

  const chart = echarts.init(pieChartRef.value)
  
  // 基于实际用户课程数据生成饼图
  const categoryStats: { [key: string]: number } = {}
  
  courseStore.userCourses.forEach(userCourse => {
    if (userCourse.status === 'completed') {
      const category = userCourse.course.category
      categoryStats[category] = (categoryStats[category] || 0) + userCourse.course.credits
    }
  })
  
  const pieData = Object.entries(categoryStats).map(([category, credits]) => ({
    value: credits,
    name: category
  }))
  
  // 如果没有数据，显示预设的学分要求分布
  const defaultData = [
    { value: 47, name: '通识必修', completed: categoryStats['通识必修'] || 0 },
    { value: 12, name: '通识选修', completed: categoryStats['通识选修'] || 0 },
    { value: 33, name: '学科基础', completed: categoryStats['学科基础'] || 0 },
    { value: 23, name: '专业必修', completed: categoryStats['专业必修'] || 0 },
    { value: 14, name: '专业选修', completed: categoryStats['专业选修'] || 0 },
    { value: 16, name: '实践教学', completed: categoryStats['实践教学'] || 0 },
    { value: 6, name: '其他', completed: categoryStats['其他'] || 0 }
  ]
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        const data = defaultData.find(d => d.name === params.name)
        if (data) {
          return `${params.name}<br/>已修: ${data.completed} 学分<br/>要求: ${data.value} 学分<br/>完成度: ${Math.round((data.completed / data.value) * 100)}%`
        }
        return `${params.name}: ${params.value} 学分`
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '学分分布',
        type: 'pie',
        radius: ['30%', '60%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: defaultData.map(item => ({
          value: item.completed,
          name: item.name,
          itemStyle: {
            color: item.completed >= item.value ? '#67c23a' : 
                   item.completed > 0 ? '#e6a23c' : '#f56c6c'
          }
        }))
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

onMounted(async () => {
  // 获取用户课程数据
  await courseStore.fetchUserCourses()
  
  // 图表需要在数据加载完成后初始化
  setTimeout(() => {
    initPieChart()
  }, 100)
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  color: #303133;
  margin-bottom: 8px;
}

.dashboard-header p {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stat-icon.total-credits {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.completed-credits {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.gpa {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.remaining-semesters {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  font-size: 32px;
  font-weight: bold;
  margin: 0;
  color: #303133;
}

.stat-info p {
  font-size: 14px;
  color: #909399;
  margin: 5px 0 0 0;
}

.charts-row {
  margin-bottom: 30px;
}

.progress-container {
  padding: 20px;
}

.progress-text {
  text-align: center;
  margin-top: 15px;
  color: #606266;
  font-size: 14px;
}

.current-semester-row {
  margin-bottom: 30px;
}

.semester-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.credits-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.total-credits {
  font-weight: bold;
  color: #409eff;
}

.credits-breakdown {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}
</style>
