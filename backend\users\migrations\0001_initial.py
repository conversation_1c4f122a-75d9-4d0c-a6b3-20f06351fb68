# Generated by Django 5.2.3 on 2025-06-24 06:42

import django.contrib.auth.models
import django.contrib.auth.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON>ield(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='邮箱')),
                ('real_name', models.CharField(blank=True, max_length=50, verbose_name='真实姓名')),
                ('student_id', models.CharField(blank=True, max_length=20, null=True, unique=True, verbose_name='学号')),
                ('school', models.CharField(blank=True, max_length=100, verbose_name='学校')),
                ('college', models.CharField(blank=True, max_length=100, verbose_name='学院')),
                ('major', models.CharField(blank=True, max_length=100, verbose_name='专业')),
                ('enrollment_year', models.IntegerField(blank=True, null=True, verbose_name='入学年份')),
                ('bio', models.TextField(blank=True, verbose_name='个人简介')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='头像')),
                ('email_verified', models.BooleanField(default=False, verbose_name='邮箱已验证')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('groups', models.ManyToManyField(blank=True, help_text='用户所属的组', related_name='custom_user_set', to='auth.group', verbose_name='用户组')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='用户拥有的权限', related_name='custom_user_set', to='auth.permission', verbose_name='用户权限')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
                'db_table': 'users',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
