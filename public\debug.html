<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由调试</title>
</head>
<body>
    <h1>路由调试页面</h1>
    <div id="debug-info">
        <h2>当前状态:</h2>
        <p id="current-url">当前URL: <span></span></p>
        <p id="has-token">是否有Token: <span></span></p>
        <p id="token-value">Token值: <span></span></p>
        <p id="user-info">用户信息: <span></span></p>
    </div>
    
    <div id="actions">
        <h2>操作:</h2>
        <button onclick="clearStorage()">清除本地存储</button>
        <button onclick="goToLogin()">跳转到登录页面</button>
        <button onclick="goToDashboard()">跳转到仪表盘</button>
        <button onclick="refreshInfo()">刷新信息</button>
    </div>

    <script>
        function refreshInfo() {
            document.querySelector('#current-url span').textContent = window.location.href;
            
            const token = localStorage.getItem('token');
            document.querySelector('#has-token span').textContent = token ? '是' : '否';
            document.querySelector('#token-value span').textContent = token ? token.substring(0, 20) + '...' : '无';
            
            const user = localStorage.getItem('user');
            document.querySelector('#user-info span').textContent = user ? JSON.parse(user).email || '解析失败' : '无';
        }
        
        function clearStorage() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            alert('本地存储已清除');
            refreshInfo();
        }
        
        function goToLogin() {
            window.location.href = '/login';
        }
        
        function goToDashboard() {
            window.location.href = '/dashboard';
        }
        
        // 页面加载时刷新信息
        refreshInfo();
        
        // 每秒更新一次信息
        setInterval(refreshInfo, 1000);
    </script>
</body>
</html>
