from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class CourseCategory(models.Model):
    """课程分类模型 - 支持嵌套结构"""
    name = models.CharField(max_length=100, verbose_name='分类名称')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                              related_name='children', verbose_name='父分类')
    description = models.TextField(blank=True, verbose_name='分类描述')

    # 学分要求 - 支持总学分和实践学分的分别要求
    total_credits_required = models.PositiveIntegerField(default=0, verbose_name='总学分要求')
    practical_credits_required = models.PositiveIntegerField(default=0, verbose_name='实践学分要求')

    # 排序字段
    order = models.PositiveIntegerField(default=0, verbose_name='排序')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '课程分类'
        verbose_name_plural = '课程分类'
        db_table = 'course_categories'
        ordering = ['order', 'name']

    def __str__(self):
        if self.parent:
            return f"{self.parent.name} - {self.name}"
        return self.name

    @property
    def full_path(self):
        """获取完整的分类路径"""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name

    def get_all_children(self):
        """获取所有子分类（递归）"""
        children = list(self.children.all())
        for child in self.children.all():
            children.extend(child.get_all_children())
        return children

class CourseTag(models.Model):
    """课程标签"""
    name = models.CharField(max_length=50, unique=True, verbose_name='标签名称')
    description = models.CharField(max_length=200, blank=True, verbose_name='描述')

    class Meta:
        verbose_name = '课程标签'
        verbose_name_plural = '课程标签'
        db_table = 'course_tags'

    def __str__(self):
        return self.name

class Course(models.Model):
    """课程模型"""
    course_code = models.CharField(max_length=20, unique=True, verbose_name='课程代码')
    course_name = models.CharField(max_length=100, verbose_name='课程名称')

    # 学分结构 - 分离理论和实践学分
    theoretical_credits = models.PositiveIntegerField(default=0, verbose_name='理论学分')
    practical_credits = models.PositiveIntegerField(default=0, verbose_name='实践学分')

    # 课程分类 - 每门课程只属于一个分类
    category = models.ForeignKey(CourseCategory, on_delete=models.PROTECT,
                                verbose_name='课程分类', help_text='每门课程只能属于一个分类')

    # 保留标签系统用于更灵活的分组和搜索
    tags = models.ManyToManyField(CourseTag, blank=True, verbose_name='课程标签')
    description = models.TextField(blank=True, verbose_name='课程描述')
    prerequisites = models.ManyToManyField('self', blank=True, verbose_name='先修课程')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '课程'
        verbose_name_plural = '课程'
        db_table = 'courses'

    def __str__(self):
        return f"{self.course_code} - {self.course_name}"

    @property
    def total_credits(self):
        """总学分"""
        return self.theoretical_credits + self.practical_credits

    @property
    def credits_breakdown(self):
        """学分构成描述"""
        if self.practical_credits > 0:
            return f"{self.total_credits}学分(理论{self.theoretical_credits}+实践{self.practical_credits})"
        return f"{self.total_credits}学分(理论)"

class UserCourse(models.Model):
    """用户课程关联模型"""
    STATUS_CHOICES = [
        ('planned', '计划中'),
        ('in-progress', '进行中'),
        ('completed', '已完成'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, verbose_name='课程')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned', verbose_name='状态')
    grade = models.CharField(max_length=10, blank=True, null=True, verbose_name='成绩')
    semester_taken = models.CharField(max_length=20, blank=True, verbose_name='修读学期')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '用户课程'
        verbose_name_plural = '用户课程'
        db_table = 'user_courses'
        unique_together = ['user', 'course']

    def __str__(self):
        return f"{self.user.username} - {self.course.course_name}"
