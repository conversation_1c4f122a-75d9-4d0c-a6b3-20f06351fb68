from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class CourseTag(models.Model):
    """课程标签"""
    name = models.CharField(max_length=50, unique=True, verbose_name='标签名称')
    description = models.CharField(max_length=200, blank=True, verbose_name='描述')

    class Meta:
        verbose_name = '课程标签'
        verbose_name_plural = '课程标签'
        db_table = 'course_tags'

    def __str__(self):
        return self.name

class Course(models.Model):
    """课程模型"""
    course_code = models.CharField(max_length=20, unique=True, verbose_name='课程代码')
    course_name = models.CharField(max_length=100, verbose_name='课程名称')
    credits = models.IntegerField(verbose_name='学分')
    tags = models.ManyToManyField(CourseTag, blank=True, verbose_name='课程标签')
    description = models.TextField(blank=True, verbose_name='课程描述')
    prerequisites = models.ManyToManyField('self', blank=True, verbose_name='先修课程')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '课程'
        verbose_name_plural = '课程'
        db_table = 'courses'

    def __str__(self):
        return f"{self.course_code} - {self.course_name}"

class UserCourse(models.Model):
    """用户课程关联模型"""
    STATUS_CHOICES = [
        ('planned', '计划中'),
        ('in-progress', '进行中'),
        ('completed', '已完成'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, verbose_name='课程')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned', verbose_name='状态')
    grade = models.CharField(max_length=10, blank=True, null=True, verbose_name='成绩')
    semester_taken = models.CharField(max_length=20, blank=True, verbose_name='修读学期')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '用户课程'
        verbose_name_plural = '用户课程'
        db_table = 'user_courses'
        unique_together = ['user', 'course']

    def __str__(self):
        return f"{self.user.username} - {self.course.course_name}"
