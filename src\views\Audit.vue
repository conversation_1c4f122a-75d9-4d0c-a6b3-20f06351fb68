<template>
  <div class="audit">    <div class="audit-header">
      <h1>毕业预审</h1>
      <p>{{ templateInfo.school }} {{ templateInfo.major }} - 最低毕业学分{{ templateInfo.totalCreditsRequired }}分</p>
      <el-button
        type="primary"
        size="large"
        @click="runAudit"
        :loading="auditLoading"
      >
        <el-icon><Refresh /></el-icon>
        重新审查
      </el-button>
    </div>

    <div class="audit-summary">
      <el-alert
        :title="auditResult.overallStatus === 'passed' ? '恭喜！您已满足毕业要求' : '还有部分要求未满足'"
        :type="auditResult.overallStatus === 'passed' ? 'success' : 'warning'"
        :description="auditResult.summary"
        show-icon
        :closable="false"
      />
    </div>

    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total-requirements">
              <el-icon><List /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ auditResult.totalRequirements }}</h3>
              <p>总要求项</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon met-requirements">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ auditResult.metRequirements }}</h3>
              <p>已满足</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon unmet-requirements">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ auditResult.unmetRequirements }}</h3>
              <p>未满足</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completion-rate">
              <el-icon><PieChart /></el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ Math.round((auditResult.metRequirements / auditResult.totalRequirements) * 100) }}%</h3>
              <p>完成率</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <div class="audit-details">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>详细审查结果</span>
            <div class="header-actions">
              <el-button @click="exportReport">
                <el-icon><Download /></el-icon>
                导出报告
              </el-button>
            </div>
          </div>
        </template>

        <el-collapse v-model="activeCollapse">
          <el-collapse-item
            v-for="category in auditDetails"
            :key="category.name"
            :title="category.name"
            :name="category.name"
          >
            <template #title>
              <div class="collapse-title">
                <span>{{ category.name }}</span>
                <div class="category-status">
                  <el-tag
                    :type="category.status === 'passed' ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ category.status === 'passed' ? '已满足' : '未满足' }}
                  </el-tag>
                  <span class="credit-info">
                    {{ category.completedCredits }} / {{ category.requiredCredits }} 学分
                  </span>
                </div>
              </div>
            </template>

            <div class="category-details">
              <div class="category-summary">
                <el-progress
                  :percentage="Math.round((category.completedCredits / category.requiredCredits) * 100)"
                  :status="category.completedCredits >= category.requiredCredits ? 'success' : undefined"
                />
                <p class="summary-text">{{ category.description }}</p>
              </div>

              <div class="requirements-list">
                <h4>具体要求：</h4>
                <div
                  v-for="requirement in category.requirements"
                  :key="requirement.id"
                  class="requirement-item"
                  :class="{ 'met': requirement.isMet }"
                >
                  <div class="requirement-header">
                    <el-icon
                      :class="requirement.isMet ? 'success-icon' : 'warning-icon'"
                    >
                      <Check v-if="requirement.isMet" />
                      <Warning v-else />
                    </el-icon>
                    <span class="requirement-name">{{ requirement.name }}</span>
                    <el-tag
                      :type="requirement.isMet ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ requirement.isMet ? '已满足' : '未满足' }}
                    </el-tag>
                  </div>
                  <p class="requirement-description">{{ requirement.description }}</p>
                  <div v-if="!requirement.isMet && requirement.suggestions" class="suggestions">
                    <h5>建议：</h5>
                    <ul>
                      <li v-for="suggestion in requirement.suggestions" :key="suggestion">
                        {{ suggestion }}
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div v-if="category.courses && category.courses.length > 0" class="completed-courses">
                <h4>已完成课程：</h4>
                <el-table :data="category.courses" size="small">
                  <el-table-column prop="courseCode" label="课程代码" width="120" />
                  <el-table-column prop="courseName" label="课程名称" />
                  <el-table-column prop="credits" label="学分" width="80" />
                  <el-table-column prop="grade" label="成绩" width="80" />
                  <el-table-column prop="semester" label="修读学期" width="150" />
                </el-table>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </div>

    <div class="recommendations">
      <el-card>
        <template #header>
          <span>建议与提醒</span>
        </template>
        <div class="recommendation-list">
          <div
            v-for="(recommendation, index) in recommendations"
            :key="index"
            class="recommendation-item"
            :class="recommendation.type"
          >
            <el-icon class="recommendation-icon">
              <InfoFilled v-if="recommendation.type === 'info'" />
              <Warning v-if="recommendation.type === 'warning'" />
              <Check v-if="recommendation.type === 'success'" />
            </el-icon>
            <div class="recommendation-content">
              <h4>{{ recommendation.title }}</h4>
              <p>{{ recommendation.content }}</p>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  List,
  Check,
  Warning,
  PieChart,
  Download,
  InfoFilled
} from '@element-plus/icons-vue'
import { curriculumAPI } from '../services/curriculum'

interface AuditResult {
  overallStatus: 'passed' | 'failed'
  summary: string
  totalRequirements: number
  metRequirements: number
  unmetRequirements: number
  auditDate: string
}

interface Requirement {
  id: string
  name: string
  description: string
  isMet: boolean
  suggestions?: string[]
}

interface AuditCategory {
  name: string
  status: 'passed' | 'failed'
  requiredCredits: number
  completedCredits: number
  description: string
  requirements: Requirement[]
  courses?: any[]
}

interface Recommendation {
  type: 'info' | 'warning' | 'success'
  title: string
  content: string
}

const auditLoading = ref(false)
const activeCollapse = ref(['通识必修课程'])

const auditResult = reactive<AuditResult>({
  overallStatus: 'failed',
  summary: '正在加载毕业预审数据...',
  totalRequirements: 0,
  metRequirements: 0,
  unmetRequirements: 0,
  auditDate: new Date().toISOString().split('T')[0]
})

const auditDetails = ref<AuditCategory[]>([])
const recommendations = ref<Recommendation[]>([])
const templateInfo = ref<any>({})

// 运行毕业预审
const runAudit = async () => {
  auditLoading.value = true
  try {
    const response = await curriculumAPI.getGraduationAudit()
    
    // 更新审查结果
    Object.assign(auditResult, response.auditResult)
    
    // 更新详细信息
    auditDetails.value = response.auditDetails
    
    // 更新建议
    recommendations.value = response.recommendations
    
    // 更新模板信息
    templateInfo.value = response.template
    
    ElMessage.success('毕业预审完成')
  } catch (error) {
    console.error('毕业预审失败:', error)
    ElMessage.error('毕业预审失败，请重试')
  } finally {
    auditLoading.value = false
  }
}

// 导出报告
const exportReport = () => {
  ElMessage.info('导出功能开发中...')
}

// 组件挂载时自动运行预审
onMounted(() => {
  runAudit()
})
</script>

<style scoped>
.audit {
  padding: 20px;
}

.audit-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.audit-header div {
  flex: 1;
}

.audit-header h1 {
  color: #303133;
  margin-bottom: 8px;
}

.audit-header p {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.audit-summary {
  margin-bottom: 30px;
}

.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stat-icon.total-requirements {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.met-requirements {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.stat-icon.unmet-requirements {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
}

.stat-icon.completion-rate {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
}

.stat-info h3 {
  font-size: 32px;
  font-weight: bold;
  margin: 0;
  color: #303133;
}

.stat-info p {
  font-size: 14px;
  color: #909399;
  margin: 5px 0 0 0;
}

.audit-details {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.collapse-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-right: 20px;
}

.category-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.credit-info {
  font-size: 14px;
  color: #909399;
}

.category-details {
  padding: 20px 0;
}

.category-summary {
  margin-bottom: 30px;
}

.summary-text {
  margin-top: 10px;
  color: #606266;
}

.requirements-list h4,
.completed-courses h4 {
  color: #303133;
  margin-bottom: 15px;
}

.requirement-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.requirement-item.met {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.requirement-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.success-icon {
  color: #67c23a;
}

.warning-icon {
  color: #e6a23c;
}

.requirement-name {
  font-weight: bold;
  flex: 1;
}

.requirement-description {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.suggestions {
  margin-top: 10px;
  padding: 10px;
  background-color: #fff7e6;
  border-radius: 4px;
}

.suggestions h5 {
  margin: 0 0 8px 0;
  color: #e6a23c;
}

.suggestions ul {
  margin: 0;
  padding-left: 20px;
}

.suggestions li {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.completed-courses {
  margin-top: 20px;
}

.recommendations {
  margin-bottom: 30px;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
}

.recommendation-item.info {
  background-color: #f0f9ff;
  border-left: 4px solid #409eff;
}

.recommendation-item.warning {
  background-color: #fdf6ec;
  border-left: 4px solid #e6a23c;
}

.recommendation-item.success {
  background-color: #f0f9ff;
  border-left: 4px solid #67c23a;
}

.recommendation-icon {
  font-size: 20px;
  margin-top: 2px;
}

.recommendation-item.info .recommendation-icon {
  color: #409eff;
}

.recommendation-item.warning .recommendation-icon {
  color: #e6a23c;
}

.recommendation-item.success .recommendation-icon {
  color: #67c23a;
}

.recommendation-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.recommendation-content p {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}
</style>
