from django.core.management.base import BaseCommand
from django.db import transaction
from courses.models import Course, CourseTag
from curriculum.models import CurriculumTemplate, RequirementModule, Rule, UserCurriculumTemplate
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = '创建一套完整的示例数据，包括课程、标签和基于规则的培养方案。'

    @transaction.atomic
    def handle(self, *args, **options):
        self.stdout.write('开始清理旧数据...')
        User.objects.filter(email='<EMAIL>').delete()
        CurriculumTemplate.objects.all().delete()
        Course.objects.all().delete()
        CourseTag.objects.all().delete()
        self.stdout.write('旧数据清理完毕。')
        
        self.stdout.write('开始创建新的示例数据...')

        # 1. 创建课程标签
        tags_data = {
            '通识必修': '公共基础必修课程',
            '学科基础': '数学、物理等大类基础课程',
            '专业必修': '专业领域的核心必修课程',
            '专业选修': '专业方向的选修课程',
            '实践课': '包含实验、实习、设计等实践环节的课程',
            '通识教育选修': '跨学科的通识教育课程',
            '自然科学类': '通识教育中的自然科学与技术类别',
            '人文社科类': '通识教育中的人文、历史、社会科学类别',
            '艺术审美类': '通识教育中的文化、艺术、审美类别'
        }
        tags = {}
        for name, desc in tags_data.items():
            tag, _ = CourseTag.objects.get_or_create(name=name, defaults={'description': desc})
            tags[name] = tag
            self.stdout.write(f'  创建标签: {name}')

        # 2. 创建课程并打上标签
        courses_data = [
            # 专业课
            {'code': 'CS101', 'name': '程序设计基础', 'credits': 4, 'tags': [tags['学科基础'], tags['实践课']]},
            {'code': 'CS201', 'name': '数据结构与算法', 'credits': 4, 'tags': [tags['专业必修'], tags['实践课']]},
            {'code': 'CS301', 'name': '计算机网络', 'credits': 3, 'tags': [tags['专业必修']]},
            {'code': 'CS302', 'name': '操作系统', 'credits': 4, 'tags': [tags['专业必修'], tags['实践课']]},
            # 专业选修课
            {'code': 'CS410', 'name': '机器学习', 'credits': 3, 'tags': [tags['专业选修']]},
            {'code': 'CS411', 'name': 'Web开发技术', 'credits': 3, 'tags': [tags['专业选修'], tags['实践课']]},
            {'code': 'CS412', 'name': '信息安全导论', 'credits': 2, 'tags': [tags['专业选修']]},
            # 通识课
            {'code': 'PHY101', 'name': '大学物理', 'credits': 4, 'tags': [tags['通识教育选修'], tags['自然科学类']]},
            {'code': 'HIS201', 'name': '世界通史', 'credits': 2, 'tags': [tags['通识教育选修'], tags['人文社科类']]},
            {'code': 'ART101', 'name': '艺术欣赏', 'credits': 2, 'tags': [tags['通识教育选修'], tags['艺术审美类']]},
            {'code': 'ECO101', 'name': '经济学原理', 'credits': 3, 'tags': [tags['通识教育选修'], tags['人文社科类']]},
        ]
        
        for c_data in courses_data:
            course, created = Course.objects.get_or_create(
                course_code=c_data['code'],
                defaults={'course_name': c_data['name'], 'credits': c_data['credits']}
            )
            if created:
                course.tags.set(c_data['tags'])
                self.stdout.write(f'  创建课程: {course.course_name}')

        # 3. 创建培养方案和规则
        template, _ = CurriculumTemplate.objects.get_or_create(
            school='示例大学', major='计算机科学', enrollment_year='2024',
            defaults={'total_credits_required': 150, 'description': '一个基于规则的示例培养方案'}
        )
        self.stdout.write(f'创建培养方案: {template}')
        
        # 模块1：专业选修课 (复杂规则)
        prof_elective_mod = RequirementModule.objects.create(
            template=template, name='专业选修课', credits_required=8
        )
        # 规则1.1: 定义课程池
        rule1 = Rule.objects.create(
            module=prof_elective_mod, name='专业选修课程池'
        )
        rule1.tags.set([tags['专业选修']])
        # 规则1.2: 子要求-实践课
        rule2 = Rule.objects.create(
            module=prof_elective_mod, name='实践学分要求', credits_required=3
        )
        rule2.tags.set([tags['实践课']])
        self.stdout.write(f'  创建要求模块: {prof_elective_mod.name} (要求8学分, 其中实践3学分)')

        # 模块2：通识教育选修课 (嵌套结构)
        gen_edu_mod = RequirementModule.objects.create(
            template=template, name='通识教育选修课', credits_required=8
        )
        # 子模块 2.1: 自然科学
        natural_sci_mod = RequirementModule.objects.create(
            template=template, parent=gen_edu_mod, name='自然科学类', credits_required=4
        )
        rule3 = Rule.objects.create(
            module=natural_sci_mod, name='自然科学类课程池'
        )
        rule3.tags.set([tags['自然科学类']])
        # 子模块 2.2: 人文社科
        humanities_mod = RequirementModule.objects.create(
            template=template, parent=gen_edu_mod, name='人文社科类', credits_required=2
        )
        rule4 = Rule.objects.create(
            module=humanities_mod, name='人文社科类课程池'
        )
        rule4.tags.set([tags['人文社科类']])
        self.stdout.write(f'  创建要求模块: {gen_edu_mod.name} (包含子模块)')

        # 4. 创建测试用户并关联培养方案
        test_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'student', 'real_name': '王五', 'student_id': '2024001001',
                'school': '示例大学', 'major': '计算机科学', 'enrollment_year': '2024'
            }
        )
        if created:
            test_user.set_password('password123')
            test_user.save()
            self.stdout.write(f'创建测试用户: {test_user.email}')
            
        UserCurriculumTemplate.objects.create(user=test_user, template=template, is_active=True)
        self.stdout.write(f'  已将用户 {test_user.username} 关联到培养方案')

        self.stdout.write(self.style.SUCCESS('示例数据创建完毕！'))
        self.stdout.write('使用 "python manage.py createsuperuser" 创建管理员账户。')
        self.stdout.write('测试用户: <EMAIL>, 密码: password123')
