#!/usr/bin/env python3
"""
学途管家 API 测试脚本
用于测试后端API的基本功能
"""

import requests
import json
import sys

BASE_URL = "http://127.0.0.1:8000"

def test_user_registration():
    """测试用户注册API"""
    print("🧪 测试用户注册API...")
    
    url = f"{BASE_URL}/api/users/register/"
    data = {
        "username": "testuser_debug",
        "email": "<EMAIL>",
        "password": "testpass123456",
        "password2": "testpass123456",
        "real_name": "测试用户",
        "student_id": "2024001",
        "school": "测试大学",
        "college": "计算机学院",
        "major": "计算机科学与技术",
        "enrollment_year": 2024
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 201:
            print("✅ 用户注册成功!")
            return response.json()
        else:
            print(f"❌ 用户注册失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None


def test_user_login():
    """测试用户登录API"""
    print("\n🧪 测试用户登录API...")
    
    url = f"{BASE_URL}/api/users/login/"
    data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 用户登录成功!")
            return response.json()
        else:
            print(f"❌ 用户登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None


def test_courses_api(token=None):
    """测试课程API"""
    print("\n🧪 测试课程列表API...")
    
    url = f"{BASE_URL}/api/courses/"
    headers = {}
    if token:
        headers['Authorization'] = f'Token {token}'
    
    try:
        response = requests.get(url, headers=headers)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            courses = response.json()
            print(f"✅ 获取到 {len(courses)} 门课程")
            if courses:
                print(f"第一门课程: {courses[0]['course_name']}")
            return courses
        else:
            print(f"❌ 获取课程列表失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None


def test_curriculum_api(token=None):
    """测试培养方案API"""
    print("\n🧪 测试培养方案API...")
    
    url = f"{BASE_URL}/api/curriculum/templates/"
    headers = {}
    if token:
        headers['Authorization'] = f'Token {token}'
    
    try:
        response = requests.get(url, headers=headers)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            templates = response.json()
            print(f"✅ 获取到 {len(templates)} 个培养方案")
            if templates:
                print(f"第一个方案: {templates[0]['school']} - {templates[0]['major']}")
            return templates
        else:
            print(f"❌ 获取培养方案失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None


def main():
    """主测试函数"""
    print("🚀 开始测试学途管家后端API...")
    print("=" * 50)
    
    # 跳过用户注册，直接测试登录
    print("ℹ️  跳过用户注册测试（用户已存在）")
    
    # 测试用户登录
    login_data = test_user_login()
    token = login_data.get('token') if login_data else None
    
    # 测试课程API
    test_courses_api(token)
    
    # 测试培养方案API
    test_curriculum_api(token)
    
    print("\n" + "=" * 50)
    print("🎯 API测试完成!")


if __name__ == "__main__":
    main()
