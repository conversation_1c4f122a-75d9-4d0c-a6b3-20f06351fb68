from rest_framework import serializers
from .models import Course, UserCourse, CourseTag, CourseCategory

class CourseCategorySerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()
    full_path = serializers.ReadOnlyField()

    class Meta:
        model = CourseCategory
        fields = ['id', 'name', 'parent', 'description', 'total_credits_required',
                 'practical_credits_required', 'order', 'full_path', 'children']

    def get_children(self, obj):
        if obj.children.exists():
            return CourseCategorySerializer(obj.children.all(), many=True).data
        return []

class CourseTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = CourseTag
        fields = '__all__'

class CourseSerializer(serializers.ModelSerializer):
    tags = CourseTagSerializer(many=True, read_only=True)
    tag_ids = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=CourseTag.objects.all(),
        source='tags',
        write_only=True,
        required=False
    )
    category = CourseCategorySerializer(read_only=True)
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=CourseCategory.objects.all(),
        source='category',
        write_only=True,
        required=False
    )
    total_credits = serializers.ReadOnlyField()
    credits_breakdown = serializers.ReadOnlyField()

    class Meta:
        model = Course
        fields = ['id', 'course_code', 'course_name', 'theoretical_credits', 'practical_credits',
                 'total_credits', 'credits_breakdown', 'category', 'category_id',
                 'description', 'prerequisites', 'tags', 'tag_ids']

class UserCourseSerializer(serializers.ModelSerializer):
    course = CourseSerializer(read_only=True)
    course_id = serializers.IntegerField(write_only=True, required=False)
    course_manual = CourseSerializer(write_only=True, required=False)
    
    class Meta:
        model = UserCourse
        fields = ['id', 'course', 'course_id', 'course_manual', 'status', 'grade', 'semester_taken', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']
        
    def validate(self, data):
        # On creation (POST), we need one of them. On update (PATCH), neither is needed.
        if not self.instance and 'course_id' not in data and 'course_manual' not in data:
            raise serializers.ValidationError("Either 'course_id' or 'course_manual' must be provided for creation.")
        if 'course_id' in data and 'course_manual' in data:
            raise serializers.ValidationError("Provide either 'course_id' or 'course_manual', not both.")
        return data

    def create(self, validated_data):
        course_manual_data = validated_data.pop('course_manual', None)
        course_id = validated_data.pop('course_id', None)
        course = None
        user = self.context['request'].user

        if course_id:
            try:
                course = Course.objects.get(pk=course_id)
            except Course.DoesNotExist:
                raise serializers.ValidationError(f"Course with id {course_id} does not exist.")
        elif course_manual_data:
            # Pop tags from manual data to handle them separately
            tags = course_manual_data.pop('tags', []) 
            
            # Create or get the course instance
            course_instance, created = Course.objects.get_or_create(
                course_code=course_manual_data['course_code'],
                defaults=course_manual_data
            )
            
            # If tags were provided, set them for the course instance
            if tags:
                course_instance.tags.set(tags)
            
            course = course_instance
        
        if not course:
             raise serializers.ValidationError("Could not find or create a course.")

        # Check for duplicates
        if UserCourse.objects.filter(user=user, course=course).exists():
            raise serializers.ValidationError({'detail': f'您已添加过课程 "{course.course_name}"'})

        validated_data['user'] = user
        validated_data['course'] = course
        return super().create(validated_data)
