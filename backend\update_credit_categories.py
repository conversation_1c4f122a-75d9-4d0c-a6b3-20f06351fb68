#!/usr/bin/env python3
"""
更新学分类别数据
根据计算机科学与技术专业的学分要求
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'academic_journey.settings')
django.setup()

from curriculum.models import CurriculumTemplate, CreditRequirement
from courses.models import Course
from django.contrib.auth import get_user_model

User = get_user_model()

def update_credit_categories():
    """更新学分类别"""
    print("🔧 更新学分类别数据...")
    
    # 获取计算机科学与技术专业的培养方案
    try:
        template = CurriculumTemplate.objects.get(
            school="某某大学", 
            major="计算机科学与技术"
        )
        print(f"找到培养方案: {template}")
        
        # 更新总学分要求
        template.total_credits_required = 151
        template.save()
        print("✅ 更新总学分要求为151")
        
        # 删除旧的学分要求
        CreditRequirement.objects.filter(template=template).delete()
        print("🗑️ 清除旧的学分要求")
        
        # 创建新的学分要求
        credit_requirements = [
            {
                'category': '通识教育必修',
                'required_credits': 47,
                'description': '思想政治理论、大学英语、高等数学、大学物理等'
            },
            {
                'category': '通识教育选修',
                'required_credits': 12,
                'description': '人文社科、自然科学、艺术体育等选修课程'
            },
            {
                'category': '学科基础',
                'required_credits': 33,
                'description': '程序设计基础、数据结构、计算机组成原理等'
            },
            {
                'category': '专业必修',
                'required_credits': 23,
                'description': '操作系统、数据库系统、软件工程等核心专业课'
            },
            {
                'category': '专业选修',
                'required_credits': 14,
                'description': '人工智能、网络安全、大数据等方向选修课'
            },
            {
                'category': '集中实践环节',
                'required_credits': 16,
                'description': '课程设计、实习、毕业设计等实践环节'
            },
            {
                'category': '课外创新创业',
                'required_credits': 2,
                'description': '创新创业实践、学科竞赛、科研项目等'
            },
            {
                'category': '素质拓展',
                'required_credits': 4,
                'description': '社会实践、志愿服务、文体活动等'
            }
        ]
        
        for req_data in credit_requirements:
            credit_req = CreditRequirement.objects.create(
                template=template,
                category=req_data['category'],
                required_credits=req_data['required_credits'],
                description=req_data['description']
            )
            print(f"✅ 创建学分要求: {credit_req.category} - {credit_req.required_credits}学分")
        
        print(f"\n📊 学分类别汇总:")
        total_credits = 0
        for req in CreditRequirement.objects.filter(template=template):
            print(f"  {req.category}: {req.required_credits}学分")
            total_credits += req.required_credits
        print(f"  总计: {total_credits}学分")
        
    except CurriculumTemplate.DoesNotExist:
        print("❌ 未找到计算机科学与技术专业的培养方案")
        return False
    
    return True

def update_course_categories():
    """更新课程类别"""
    print("\n🔧 更新课程类别...")
    
    # 更新现有课程的类别
    course_categories = {
        '程序设计基础': '学科基础',
        '数据结构与算法': '学科基础',
        '计算机组成原理': '学科基础',
        '操作系统': '专业必修',
        '数据库系统原理': '专业必修',
        '计算机网络': '专业必修',
        '软件工程': '专业必修',
        '人工智能导论': '专业选修',
        '网络安全': '专业选修',
        '大数据技术': '专业选修'
    }
    
    for course_name, category in course_categories.items():
        try:
            course = Course.objects.get(course_name=course_name)
            course.category = category
            course.save()
            print(f"✅ 更新课程类别: {course_name} -> {category}")
        except Course.DoesNotExist:
            print(f"⚠️ 课程不存在: {course_name}")
    
    print("\n📚 课程类别分布:")
    categories = Course.objects.values_list('category', flat=True).distinct()
    for category in categories:
        count = Course.objects.filter(category=category).count()
        print(f"  {category}: {count}门课程")

def main():
    print("🚀 开始更新学分类别数据...")
    print("=" * 50)
    
    if update_credit_categories():
        update_course_categories()
        print("\n" + "=" * 50)
        print("✅ 学分类别数据更新完成!")
    else:
        print("\n" + "=" * 50)
        print("❌ 更新失败!")

if __name__ == "__main__":
    main()
