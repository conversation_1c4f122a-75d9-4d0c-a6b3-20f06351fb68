import api from '../utils/api'

// Based on backend/courses/models.py Course model
export interface Course {
  id: number
  course_code: string
  course_name: string
  credits: number
  category: string
  description: string
  prerequisites: number[]
}

// Based on backend/courses/serializers.py UserCourseSerializer (for reading)
export interface UserCourse {
  id: number
  course: Course
  status: 'planned' | 'in-progress' | 'completed'
  grade: string | null
  semester_taken: string
  created_at: string
  updated_at: string
}

// For creating a UserCourse, we might not have a full course object
// We might just be adding a course from the general course list
export interface NewUserCourseData {
  course_id: number
  status: 'planned' | 'in-progress' | 'completed'
  grade?: string | null
  semester_taken: string
}

// For updating a UserCourse
export type UpdateUserCourseData = Partial<NewUserCourseData>

export const courseAPI = {
  // Get all of the user's courses
  getUserCourses: (): Promise<UserCourse[]> => {
    return api.get('/courses/user-courses/')
  },

  // Add a course to the user's journey
  addUserCourse: (data: NewUserCourseData): Promise<UserCourse> => {
    return api.post('/courses/user-courses/', data)
  },

  // Update a user's course details
  updateUserCourse: (
    id: number,
    data: UpdateUserCourseData
  ): Promise<UserCourse> => {
    return api.patch(`/courses/user-courses/${id}/`, data)
  },

  // Delete a course from the user's journey
  deleteUserCourse: (id: number): Promise<void> => {
    return api.delete(`/courses/user-courses/${id}/`)
  },

  // Get the list of all available courses in the system
  getAllCourses: (): Promise<Course[]> => {
    return api.get('/courses/')
  }
}

export default courseAPI
