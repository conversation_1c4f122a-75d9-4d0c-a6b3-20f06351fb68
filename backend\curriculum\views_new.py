from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import CurriculumTemplate, TemplateCourse, CreditRequirement
from .serializers import (
    CurriculumTemplateSerializer, 
    TemplateCourseSerializer, 
    CreditRequirementSerializer
)


class CurriculumTemplateViewSet(viewsets.ModelViewSet):
    queryset = CurriculumTemplate.objects.all()
    serializer_class = CurriculumTemplateSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """根据用户的学校和专业过滤培养方案"""
        user = self.request.user
        queryset = CurriculumTemplate.objects.all()
        
        # 如果用户有学校和专业信息，优先显示匹配的方案
        if user.school and user.major:
            queryset = queryset.filter(school=user.school, major=user.major)
        
        return queryset
    
    @action(detail=True, methods=['get'])
    def template_courses(self, request, pk=None):
        """获取培养方案的课程"""
        template = self.get_object()
        courses = TemplateCourse.objects.filter(template=template)
        serializer = TemplateCourseSerializer(courses, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def credit_requirements(self, request, pk=None):
        """获取培养方案的学分要求"""
        template = self.get_object()
        requirements = CreditRequirement.objects.filter(template=template)
        serializer = CreditRequirementSerializer(requirements, many=True)
        return Response(serializer.data)


class TemplateCourseViewSet(viewsets.ModelViewSet):
    queryset = TemplateCourse.objects.all()
    serializer_class = TemplateCourseSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """根据模板ID过滤课程"""
        queryset = TemplateCourse.objects.all()
        template_id = self.request.query_params.get('template_id')
        if template_id:
            queryset = queryset.filter(template_id=template_id)
        return queryset


class CreditRequirementViewSet(viewsets.ModelViewSet):
    queryset = CreditRequirement.objects.all()
    serializer_class = CreditRequirementSerializer
    permission_classes = [IsAuthenticated]
