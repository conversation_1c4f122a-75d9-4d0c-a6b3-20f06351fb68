# Generated by Django 5.2.3 on 2025-06-24 13:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('courses', '0004_coursetag_remove_course_category_course_tags'),
        ('curriculum', '0002_usercreditrequirement'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='templatecourse',
            name='category',
        ),
        migrations.CreateModel(
            name='RequirementModule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='要求模块名称')),
                ('description', models.TextField(blank=True, verbose_name='模块描述')),
                ('credits_required', models.PositiveIntegerField(default=0, verbose_name='总学分要求')),
                ('courses_required', models.PositiveIntegerField(default=0, verbose_name='总课程数要求')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='curriculum.requirementmodule', verbose_name='父模块')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='modules', to='curriculum.curriculumtemplate', verbose_name='培养方案模板')),
            ],
            options={
                'verbose_name': '要求模块',
                'verbose_name_plural': '要求模块',
                'db_table': 'requirement_modules',
                'unique_together': {('template', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Rule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='规则名称')),
                ('description', models.TextField(blank=True, verbose_name='规则描述')),
                ('credits_required', models.PositiveIntegerField(default=0, verbose_name='要求学分')),
                ('courses_required', models.PositiveIntegerField(default=0, verbose_name='要求课程数')),
                ('tag_match_logic', models.CharField(choices=[('ANY', '满足任一标签'), ('ALL', '满足所有标签')], default='ANY', max_length=3, verbose_name='标签匹配逻辑')),
                ('module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rules', to='curriculum.requirementmodule', verbose_name='所属模块')),
                ('tags', models.ManyToManyField(to='courses.coursetag', verbose_name='所需课程标签')),
            ],
            options={
                'verbose_name': '规则',
                'verbose_name_plural': '规则',
                'db_table': 'rules',
            },
        ),
        migrations.DeleteModel(
            name='CreditRequirement',
        ),
    ]
