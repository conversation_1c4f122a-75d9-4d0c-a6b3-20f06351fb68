# Generated by Django 5.2.3 on 2025-06-24 06:42

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('course_code', models.CharField(max_length=20, unique=True, verbose_name='课程代码')),
                ('course_name', models.CharField(max_length=100, verbose_name='课程名称')),
                ('credits', models.IntegerField(verbose_name='学分')),
                ('description', models.TextField(blank=True, verbose_name='课程描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('prerequisites', models.ManyToManyField(blank=True, to='courses.course', verbose_name='先修课程')),
            ],
            options={
                'verbose_name': '课程',
                'verbose_name_plural': '课程',
                'db_table': 'courses',
            },
        ),
        migrations.CreateModel(
            name='UserCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('planned', '计划中'), ('in-progress', '进行中'), ('completed', '已完成')], default='planned', max_length=20, verbose_name='状态')),
                ('grade', models.CharField(blank=True, max_length=10, verbose_name='成绩')),
                ('semester_taken', models.CharField(blank=True, max_length=20, verbose_name='修读学期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='courses.course', verbose_name='课程')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户课程',
                'verbose_name_plural': '用户课程',
                'db_table': 'user_courses',
                'unique_together': {('user', 'course')},
            },
        ),
    ]
