<template>
  <div class="module-node" :class="{ 'is-root': isRoot }">
    <div class="module-header" @click="isExpanded = !isExpanded">
      <div class="header-main">
        <div class="status-icon">
          <el-icon :size="24" :color="module.is_met ? '#67c23a' : '#f56c6c'">
            <CircleCheckFilled v-if="module.is_met" />
            <CircleCloseFilled v-else />
          </el-icon>
        </div>
        <div class="module-info">
          <h3 class="module-name">{{ module.name }}</h3>
          <p class="module-desc">{{ module.description }}</p>
        </div>
      </div>
      <div class="header-progress">
        <el-progress 
          :percentage="progressPercentage" 
          :stroke-width="10"
          :color="progressColor"
          striped
          striped-flow
        >
          <span>{{ module.credits_completed }} / {{ module.credits_required }} C</span>
        </el-progress>
      </div>
       <div class="expand-icon">
        <el-icon :class="{'is-expanded': isExpanded}"><ArrowDown /></el-icon>
      </div>
    </div>

    <el-collapse-transition>
      <div v-show="isExpanded" class="module-content">
        <!-- 规则列表 -->
        <div v-if="module.rules && module.rules.length > 0" class="rules-section">
          <h4><el-icon><Guide /></el-icon>模块规则</h4>
          <div class="rule-item" v-for="rule in module.rules" :key="rule.id">
            <div class="rule-status">
              <el-icon :size="16" :color="rule.is_met ? '#67c23a' : '#f56c6c'">
                <SuccessFilled v-if="rule.is_met" />
                <WarningFilled v-else />
              </el-icon>
            </div>
            <div class="rule-details">
              <strong>{{ rule.name }}:</strong>
              <span>要求 <strong>{{ rule.credits_required }}</strong> 学分, 已完成 <strong>{{ rule.credits_completed }}</strong> 学分。</span>
              <el-tooltip :content="rule.description" placement="top">
                <el-icon style="margin-left: 4px; color: #909399;"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </div>
        </div>

        <!-- 子模块列表 -->
        <div v-if="module.children && module.children.length > 0" class="children-section">
          <h4><el-icon><Files /></el-icon>子模块要求</h4>
          <RequirementModuleNode 
            v-for="child in module.children" 
            :key="child.id" 
            :module="child"
            :is-root="false"
          />
        </div>

         <!-- 满足课程列表 -->
        <div v-if="module.met_courses && module.met_courses.length > 0" class="courses-section">
           <h4><el-icon><ReadingLamp /></el-icon>已修读的相关课程</h4>
           <div class="met-courses-list">
             <el-tag 
                v-for="course in module.met_courses" 
                :key="course.courseCode"
                type="info"
                class="course-tag"
            >
               {{ course.courseName }} ({{ course.credits }}学分)
            </el-tag>
           </div>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import type { PropType } from 'vue';
import { RequirementModule } from '../services/curriculum';
import { 
  CircleCheckFilled, 
  CircleCloseFilled,
  SuccessFilled,
  WarningFilled,
  InfoFilled,
  ArrowDown,
  Guide,
  Files,
  ReadingLamp
} from '@element-plus/icons-vue';

// 递归组件需要一个 name
defineOptions({
  name: 'RequirementModuleNode'
})

const props = defineProps({
  module: {
    type: Object as PropType<RequirementModule>,
    required: true,
  },
  isRoot: {
    type: Boolean,
    default: true
  }
});

const isExpanded = ref(props.isRoot); // 根模块默认展开

const progressPercentage = computed(() => {
  if (props.module.credits_required === 0) {
    return props.module.is_met ? 100 : 0;
  }
  const percentage = (props.module.credits_completed / props.module.credits_required) * 100;
  return Math.min(percentage, 100);
});

const progressColor = computed(() => {
  if (props.module.is_met) return '#67c23a';
  if (progressPercentage.value > 50) return '#409eff';
  return '#f56c6c';
});
</script>

<style scoped>
.module-node {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  margin-bottom: 16px;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

.module-node.is-root {
  border-width: 2px;
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  cursor: pointer;
  gap: 16px;
}

.header-main {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-width: 0;
}

.status-icon {
  flex-shrink: 0;
}

.module-info {
  flex: 1;
  min-width: 0;
}

.module-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #1f2937;
}

.module-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-progress {
  width: 200px;
  flex-shrink: 0;
}

.header-progress span {
  font-size: 12px;
  color: white;
  font-weight: bold;
}

.expand-icon {
  color: #9ca3af;
  transition: transform 0.3s ease;
}

.expand-icon .is-expanded {
  transform: rotate(180deg);
}

.module-content {
  padding: 0 24px 24px 24px;
  border-top: 1px solid #f3f4f6;
}

.rules-section, .children-section, .courses-section {
  margin-top: 20px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
}

h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  font-size: 14px;
}

.rule-details {
  color: #4b5563;
}

.children-section {
  border-left: 3px solid #667eea;
  padding-left: 20px;
}

.met-courses-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.course-tag {
  font-size: 13px;
}
</style> 